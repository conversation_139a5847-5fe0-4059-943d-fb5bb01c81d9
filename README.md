# Inteli - Instituto de Tecnologia e Liderança

<p align="center">
<a href= "https://www.inteli.edu.br/"><img src="assets/inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
</p>

# Projeto: InfoPepper

## Grupo Chillibinos

## :student: Integrantes: 

- <PERSON> - [ [Github](https://github.com/GabriellReisss) | [LinkedIn](https://www.linkedin.com/in/gabriel-reis-07170727b/) ]

- <PERSON> - [ [Github](https://github.com/Zanette00) | [LinkedIn](https://www.linkedin.com/in/gabriel-c-zanette/) ]

- <PERSON> - [ [Github](https://github.com/JoaoAidar) | [LinkedIn](https://www.linkedin.com/in/jo%C3%A3o-aidar-689097246/) ]

- <PERSON> - [ [Github](https://github.com/le<PERSON>) | [LinkedIn](https://www.linkedin.com/in/leonardoramosvieira/) ]

- <PERSON> Josué - [ [Github](https://github.com/J05UE-l) | [LinkedIn](https://www.linkedin.com/in/rafael-josue/) ]

- Samuel Vono Godoi Chiovato - [ [Github](https://github.com/V0no) | [LinkedIn](https://www.linkedin.com/in/samuel-vono) ]

- Yan Dimitri Kruziski - [ [Github](https://github.com/yankruziski) | [LinkedIn](https://www.linkedin.com/in/yan-dimitri-kruziski/) ]

## :teacher: Professores:
### Orientador(a) 
- <a href="https://www.linkedin.com/in/laizaribeiro/">Laíza Ribeiro Silva</a>
### Instrutores
- <a href="https://www.linkedin.com/in/cristiano-benites-ph-d-687647a8/">Cristiano da Silva Benites</a>
- <a href="https://www.linkedin.com/in/pedroteberga/">Pedro Marins Freire Teberga</a> 
- <a href="https://www.linkedin.com/in/bruna-mayer/">Bruna Mayer Costa</a> 
- <a href="https://www.linkedin.com/in/geraldo-magela-severino-vasconcelos-22b1b220/">Geraldo Magela Severino Vasconcelos</a>
- <a href="https://www.linkedin.com/in/marcelo-gon%C3%A7alves-phd-a550652/">Marcelo Luizdo Amaral Gonçalves</a> 



## 📝 Descrição

Este projeto tem como objetivo construir um pipeline reprodutível de preparação, análise exploratória, modelagem de baselines e modelo de potencial de venda cross-sectional a partir de um dataset de vendas fornecido (Chilli Beans). A solução proposta segue princípios de transparência, documentação forte (todas as células comentadas em PT-BR) e entrega incremental de artefatos: dados limpos (CSV/Parquet), relatórios de QA, métricas de modelos e gráficos de EDA/Explainability. O fluxo contempla: (1) configuração e padronização de colunas para um esquema canônico; (2) limpeza (tipos, nulos, duplicatas, normalização monetária em centavos); (3) EDA com foco em distribuição temporal, contribuição de lojas e heterogeneidade geográfica / por tipo de PDV; (4) construção de baselines de previsão diária por loja (média móvel 28d e sazonal ingênuo 7d) avaliados por WAPE, sMAPE e MAE em split temporal 80/20; (5) treinamento de um modelo LightGBM para estimar potencial (usar features de idade da loja, geografia, calendário e Tipo_PDV); (6) explainability com SHAP para priorização de drivers; (7) export organizado em `reports/plots` e `reports/tables`; (8) checklist final (Definition of Done) garantindo reprodutibilidade. O horizonte inicial cobre ~5 dias de trabalho com possibilidade de extensão para análises geoespaciais opcionais (choropleth ou ranking+treemap). Limitações e premissas são registradas ao longo do notebook principal.

<b>Link para vídeo demonstrativo (placeholder):</b> a definir.

Caso exista uma aplicação web de exposição de resultados, o link será incluído aqui futuramente.

## 📁 Estrutura de pastas

Dentre os arquivos presentes na raiz do projeto, definem-se:

- <b>readme.md</b>: arquivo que serve como guia e explicação geral sobre o projeto (o mesmo que você está lendo agora).

- <b>assets</b>: todas as imagens e mídias utilizadas nos notebooks e documentação são posicionadas aqui.

- <b>documents</b>: aqui estarão todos os documentos do projeto. Há também uma pasta denominada <b>extras</b> onde estão presentes documentos complementares.


## 🔗 Acessos Rápidos
- Notebook (Colab-ready): [notebooks/chilli_beans_analysis.ipynb](notebooks/chilli_beans_analysis.ipynb)
- Sumário de resultados (CSV): [reports/2025-08-15/tables/summary.csv](reports/2025-08-15/tables/summary.csv)
- Visualizações-chave:
  - Heatmap (Estado): [reports/2025-08-15/plots/heatmap_revenue_by_state.png](reports/2025-08-15/plots/heatmap_revenue_by_state.png)
  - Heatmap (Cidade): [reports/2025-08-15/plots/heatmap_revenue_by_city.png](reports/2025-08-15/plots/heatmap_revenue_by_city.png)
  - Ranking UF: [reports/2025-08-15/plots/ranking_uf.png](reports/2025-08-15/plots/ranking_uf.png)
  - Notebook (HTML export): [notebooks/chilli_beans_analysis.html](notebooks/chilli_beans_analysis.html)

  - Correlação (Spearman): [reports/2025-08-15/plots/heatmap_corr.png](reports/2025-08-15/plots/heatmap_corr.png)


- <b>notebooks</b>: todos os Jupyter Notebooks criados para desenvolvimento do projeto.

## 💻 Execução (Ambiente Local VS Code)

1. Criar ambiente virtual
    ````bash
    python -m venv .venv
    .venv\\Scripts\\activate
    ````
2. Instalar dependências principais
    ````bash
    pip install -U pip
    pip install pandas numpy openpyxl matplotlib plotly scikit-learn lightgbm shap pyarrow jupyter
    ````
3. (Opcional) Congelar versão
    ````bash
    pip freeze > requirements.txt
    ````
4. Abrir o notebook principal em `notebooks/chilli_beans_analysis.ipynb` e executar de cima para baixo.

### Execução em Google Colab
1. Fazer upload do repositório ou montar o Drive.
2. Fixar versões para reprodutibilidade (célula no início do notebook):
    ````python
    %pip install -q pandas>=1.5.0 numpy>=1.21.0 scipy>=1.9.0 scikit-learn>=1.1.0 seaborn>=0.12.0 matplotlib>=3.5.0
    ```
3. Carregar arquivo Excel original em `data/raw/` (manter o nome ou atualizar no bloco de Config do notebook).
4. Executar todas as células sequencialmente. Salve uma cópia em seu Drive para preservar alterações.

> Observação: sem salvar uma cópia no Drive (File > Save a copy in Drive), mudanças serão perdidas ao encerrar a sessão.

## 📦 Artefatos Gerados (principais)
| Caminho | Descrição |
|---------|-----------|
| `data/clean/cleaned.csv` | Dataset limpo em CSV |
| `data/clean/cleaned.parquet` | Dataset limpo em Parquet (se suportado) |
| `reports/tables/qa_overview.csv` | Resumo de qualidade dos dados |
| `reports/tables/baseline_metrics.csv` | Métricas dos baselines por loja |
| `reports/tables/gbm_metrics.csv` | Métricas do modelo LightGBM |
| `reports/plots/` | Gráficos EDA, baselines, modelo, SHAP |
| `docs/data_dictionary.md` | Dicionário de dados canônicos |

## 🗃 Histórico de lançamentos

* 1.0.0 - 11/10/2024
    * [sprint 5] Lançamento da primeira versão do modelo preditivo com documentação.
* 0.6.0 - 27/09/2024
    * [sprint 4] Comparação de modelos preditivos
* 0.3.1 - 13/09/2024
    * [sprint 3] Preparação de dados e modelo preditivo preliminar
* 0.2.7 - 30/08/2024
    * [sprint 2] Análise exploratória e levantamento de hipóteses
* 0.1.3 - 16/08/2024
    * [sprint 1] Documentação de entendimento do negócio

## 📋 Licença/License

<img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/cc.svg?ref=chooser-v1"><img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/by.svg?ref=chooser-v1"><p xmlns:cc="http://creativecommons.org/ns#" xmlns:dct="http://purl.org/dc/terms/"><a property="dct:title" rel="cc:attributionURL" href="https://github.dev/Intelihub/Template_M3">MODELO GIT INTELI</a> by Inteli is licensed under <a href="http://creativecommons.org/licenses/by/4.0/?ref=chooser-v1" target="_blank" rel="license noopener noreferrer" style="display:inline-block;">Attribution 4.0 International</a>.</p>
