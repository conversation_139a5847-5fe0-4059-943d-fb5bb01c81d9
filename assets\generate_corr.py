from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

BASE_DIR = Path(__file__).resolve().parents[1]
REPORTS_DIR = BASE_DIR / 'reports' / '2025-08-15'
PLOTS_DIR = REPORTS_DIR / 'plots'
TABLES_DIR = REPORTS_DIR / 'tables'
CLEAN_DATA = BASE_DIR / 'data' / 'clean' / 'cleaned.csv'

PLOTS_DIR.mkdir(parents=True, exist_ok=True)
TABLES_DIR.mkdir(parents=True, exist_ok=True)

if not CLEAN_DATA.exists():
    raise SystemExit(f"Arquivo não encontrado: {CLEAN_DATA}")

# Load
try:
    df = pd.read_csv(CLEAN_DATA, parse_dates=['data'])
except Exception:
    df = pd.read_csv(CLEAN_DATA)

num_cols = [c for c in ['valor','qtd'] if c in df.columns]
if len(num_cols) >= 2:
    corr = df[num_cols].corr(method='spearman')
    # Save CSV
    corr.to_csv(TABLES_DIR / 'corr_matrix.csv', index=True)
    # Save PNG
    fig = plt.figure(figsize=(5,4), dpi=150)
    sns.heatmap(corr, annot=True, cmap='coolwarm', vmin=-1, vmax=1)
    plt.title('Matriz de Correlação (Spearman)')
    plt.tight_layout()
    out_png = PLOTS_DIR / 'heatmap_corr.png'
    fig.savefig(out_png, bbox_inches='tight')
    print('Gerado:', out_png)
    print('Gerado:', TABLES_DIR / 'corr_matrix.csv')
else:
    print('Variáveis numéricas insuficientes para correlação (esperado: valor e qtd).')

