"""Simple validation script to ensure there are no empty directories and to check report artifacts.
Run: python validate_structure.py
"""
import os
from pathlib import Path

ROOT = Path(__file__).resolve().parent

empty = []
ignore_dirs = {'.git', 'venv', '.vscode', '__pycache__'}
for root, dirs, files in os.walk(ROOT):
    for d in dirs:
        if d in ignore_dirs:
            continue
        p = Path(root) / d
        try:
            if len(os.listdir(p)) == 0:
                empty.append(str(p.relative_to(ROOT)))
        except Exception:
            pass

print("Empty directories:", empty if empty else "none")

plots = ROOT / 'reports' / '2025-08-15' / 'plots'
tables = ROOT / 'reports' / '2025-08-15' / 'tables'

if plots.exists():
    pngs = [f for f in os.listdir(plots) if f.endswith('.png')]
    print("PNG count:", len(pngs))
else:
    print("plots directory not found")

if tables.exists():
    csvs = [f for f in os.listdir(tables) if f.endswith('.csv')]
    print("CSV count:", len(csvs))
else:
    print("tables directory not found")

