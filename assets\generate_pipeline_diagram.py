# Generates assets/metodologia_pipeline.png without external CLIs.
# Uses matplotlib to draw the 8-step pipeline boxes and arrows.

import os
from pathlib import Path

# Defer matplotlib import to allow environments without it to skip gracefully
try:
    import matplotlib.pyplot as plt
    from matplotlib.patches import FancyBboxPatch, FancyArrowPatch
except Exception as e:
    print("Erro ao importar matplotlib:", e)
    raise

ASSETS = Path(__file__).resolve().parent
OUT = ASSETS / "metodologia_pipeline.png"

steps = [
    "Ingestão\n(Excel data/raw)",
    "Padronização\n(colunas)",
    "Limpeza\n& tipagem",
    "EDA\n& métricas",
    "Baselines\npor loja",
    "Modelo\n(GBM / fallback)",
    "Explainability\n(SHAP)",
    "Geografia\n(choropleth/ranking)",
    "Exportação\n(reports/AAAA-MM-DD)",
]

# Layout: horizontal flow
W, H = 2200, 450  # pixels
fig_w, fig_h = W / 100, H / 100
fig = plt.figure(figsize=(fig_w, fig_h), dpi=100)
ax = plt.gca()
ax.set_xlim(0, W)
ax.set_ylim(0, H)
ax.axis('off')

box_w, box_h = 230, 140
margin_left = 60
spacing = 30
start_x = margin_left
center_y = H/2

boxes = []
for i, label in enumerate(steps):
    x = start_x + i * (box_w + spacing)
    if x + box_w > W - margin_left:
        # wrap to next row if needed
        # For our 9 items, keep in a single row by increasing canvas width if necessary
        pass
    box = FancyBboxPatch((x, center_y - box_h/2), box_w, box_h,
                         boxstyle="round,pad=0.02,rounding_size=12",
                         linewidth=1.8, edgecolor="#1f77b4", facecolor="#e8f1fb")
    ax.add_patch(box)
    ax.text(x + box_w/2, center_y, label, ha='center', va='center', fontsize=12)
    boxes.append((x, center_y - box_h/2, box_w, box_h))

# arrows between boxes
for i in range(len(boxes) - 1):
    x, y, w, h = boxes[i]
    nx, ny, nw, nh = boxes[i+1]
    start = (x + w, y + h/2)
    end = (nx, ny + nh/2)
    arr = FancyArrowPatch(start, end, arrowstyle='-|>', mutation_scale=15, linewidth=1.5, color="#444")
    ax.add_patch(arr)

fig.tight_layout()
fig.savefig(OUT, bbox_inches='tight')
print("Gerado:", OUT)

