{"cells": [{"cell_type": "markdown", "id": "0e35c55b", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>s (Colab)\n", "\n", "> Notebook preparado para execução no Google Colab. Integra os resultados em `reports/2025-08-15/`."]}, {"cell_type": "code", "execution_count": 1, "id": "4b81e76b", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:08.437648Z", "iopub.status.busy": "2025-08-20T18:11:08.437648Z", "iopub.status.idle": "2025-08-20T18:11:25.834989Z", "shell.execute_reply": "2025-08-20T18:11:25.834989Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PLOTS_DIR: ..\\reports\\2025-08-15\\plots\n", "TABLES_DIR: ..\\reports\\2025-08-15\\tables\n", "CLEAN_DATA: ..\\data\\clean\\cleaned.csv\n", "sys.path atualizado com E:\\AI stuff\\2025-2A-T17-IN03-G04\\src\n"]}], "source": ["#@title Preparação do Ambiente\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from warnings import filterwarnings; filterwarnings('ignore')\n", "BASE_DIR = Path('.')\n", "# Fallback quando executado a partir da pasta notebooks\n", "if not (BASE_DIR / 'data' / 'clean' / 'cleaned.csv').exists():\n", "    BASE_DIR = Path('..')\n", "REPORTS_DIR = BASE_DIR / 'reports' / '2025-08-15'\n", "PLOTS_DIR = REPORTS_DIR / 'plots'\n", "TABLES_DIR = REPORTS_DIR / 'tables'\n", "CLEAN_DATA = BASE_DIR / 'data' / 'clean' / 'cleaned.csv'\n", "print('PLOTS_DIR:', PLOTS_DIR)\n", "print('TABLES_DIR:', TABLES_DIR)\n", "print('CLEAN_DATA:', CLEAN_DATA)\n", "\n", "# Atualiza sys.path para permitir imports do pacote src/\n", "import sys\n", "PROJ_ROOT = BASE_DIR.resolve()\n", "SRC_DIR = PROJ_ROOT / 'src'\n", "for p in [str(PROJ_ROOT), str(SRC_DIR)]:\n", "    if p not in sys.path:\n", "        sys.path.insert(0, p)\n", "print('sys.path atualizado com', SRC_DIR)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "fefc0c20", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:25.837998Z", "iopub.status.busy": "2025-08-20T18:11:25.837998Z", "iopub.status.idle": "2025-08-20T18:11:25.898960Z", "shell.execute_reply": "2025-08-20T18:11:25.898960Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["src package loaded\n", "OK: src.analysis_stats\n", "OK: src.baselines\n", "OK: src.features\n", "OK: src.geo_utils\n", "OK: src.io_utils\n", "OK: src.metrics\n"]}], "source": ["#@title Teste de imports do pacote src\n", "mods = ['analysis_stats','baselines','features','geo_utils','io_utils','metrics']\n", "for m in mods:\n", "    try:\n", "        __import__(f'src.{m}')\n", "        print(f'OK: src.{m}')\n", "    except Exception as e:\n", "        print(f'FALHA: src.{m} -> {e}')\n"]}, {"cell_type": "code", "execution_count": 3, "id": "cd78bf60", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:25.902033Z", "iopub.status.busy": "2025-08-20T18:11:25.901035Z", "iopub.status.idle": "2025-08-20T18:11:25.907544Z", "shell.execute_reply": "2025-08-20T18:11:25.907544Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Random seed set to 42\n"]}], "source": ["#@title (Opcional) Fixar versões para reprodutibilidade (Colab)\n", "DO_PIP = False\n", "if DO_PIP:\n", "    import IPython\n", "    IPython.get_ipython().run_line_magic('pip', 'install -q pandas>=1.5.0 numpy>=1.21.0 scipy>=1.9.0 scikit-learn>=1.1.0 seaborn>=0.12.0 matplotlib>=3.5.0 statsmodels>=0.13.0')\n", "import numpy as np\n", "RANDOM_SEED = 42\n", "np.random.seed(RANDOM_SEED)\n", "print('Random seed set to', RANDOM_SEED)\n"]}, {"cell_type": "markdown", "id": "e288cb24", "metadata": {}, "source": ["## 1. Exploração de Dados (EDA)\n", "- Variáveis numéricas: `valor`, `qtd`\n", "- Categóricas: `uf`, `cidade`, `Tipo_PDV`\n", "- Gráficos: <PERSON><PERSON><PERSON> por UF; <PERSON><PERSON>ita média por dia da semana; Top 20 cidades; Correla<PERSON>pearman (valor×qtd)"]}, {"cell_type": "code", "execution_count": 4, "id": "5d32cc74", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:25.909744Z", "iopub.status.busy": "2025-08-20T18:11:25.909744Z", "iopub.status.idle": "2025-08-20T18:11:26.485421Z", "shell.execute_reply": "2025-08-20T18:11:26.485421Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID_Faturamento</th>\n", "      <th>id_loja</th>\n", "      <th>ID_Vendedor</th>\n", "      <th>ID_Cliente</th>\n", "      <th>data</th>\n", "      <th>id_produto</th>\n", "      <th>Documento</th>\n", "      <th>DOC_UNICO</th>\n", "      <th>Transacao</th>\n", "      <th>Natureza_Operacao</th>\n", "      <th>...</th>\n", "      <th>Dim_Produtos.Cor1</th>\n", "      <th>Dim_Produtos.Cor2</th>\n", "      <th>Dim_Produtos.Material1</th>\n", "      <th>Dim_Produtos.Material2</th>\n", "      <th>Dim_Produtos.Segmentacao</th>\n", "      <th>Dim_Produtos.Shape</th>\n", "      <th>Dim_Produtos.Formato</th>\n", "      <th>Dim_Produtos.Sexo</th>\n", "      <th>Dim_Produtos.Griffe</th>\n", "      <th>Dim_Produtos.GRUPO_CHILLI</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>96910220834170</td>\n", "      <td>36667417009863</td>\n", "      <td>76787</td>\n", "      <td>7774565</td>\n", "      <td>2025-03-01</td>\n", "      <td>17274</td>\n", "      <td>9015</td>\n", "      <td>3666741700986390152025-03-01</td>\n", "      <td>834170</td>\n", "      <td>VENDA DE MERCADORIA</td>\n", "      <td>...</td>\n", "      <td>SORTIDO</td>\n", "      <td>SORTIDO</td>\n", "      <td>DIVERSOS</td>\n", "      <td>ESTOJO</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ACESSORIOS</td>\n", "      <td>UNISSEX</td>\n", "      <td>CHILLI BEANS</td>\n", "      <td>ACESSORIOS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5460502321150</td>\n", "      <td>15425148000696</td>\n", "      <td>78850</td>\n", "      <td>20112062</td>\n", "      <td>2025-03-01</td>\n", "      <td>214511</td>\n", "      <td>5225</td>\n", "      <td>1542514800069652252025-03-01</td>\n", "      <td>2321150</td>\n", "      <td>VENDA DE MERCADORIA</td>\n", "      <td>...</td>\n", "      <td>VERDE</td>\n", "      <td>BRILHO</td>\n", "      <td>TR90</td>\n", "      <td>POLARIZADA</td>\n", "      <td>CASUAL</td>\n", "      <td>QUADRADO</td>\n", "      <td>QUADRADO</td>\n", "      <td>MASCULINO</td>\n", "      <td>CHILLI BEANS</td>\n", "      <td>OCULOS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67970100951576</td>\n", "      <td>53390229006953</td>\n", "      <td>77007</td>\n", "      <td>20111742</td>\n", "      <td>2025-03-01</td>\n", "      <td>214506</td>\n", "      <td>4159</td>\n", "      <td>5339022900695341592025-03-01</td>\n", "      <td>951576</td>\n", "      <td>VENDA DE MERCADORIA</td>\n", "      <td>...</td>\n", "      <td>MARROM</td>\n", "      <td>VERDE</td>\n", "      <td>ACETATO</td>\n", "      <td>NYLON</td>\n", "      <td>CASUAL</td>\n", "      <td>QUADRADO</td>\n", "      <td>QUADRADO</td>\n", "      <td>FEMININO</td>\n", "      <td>PREMIUM</td>\n", "      <td>OCULOS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1856605015822</td>\n", "      <td>55374963018718</td>\n", "      <td>78728</td>\n", "      <td>20110119</td>\n", "      <td>2025-03-01</td>\n", "      <td>214503</td>\n", "      <td>1304</td>\n", "      <td>5537496301871813042025-03-01</td>\n", "      <td>15822</td>\n", "      <td>VENDA DE MERCADORIA</td>\n", "      <td>...</td>\n", "      <td>DEGRADE</td>\n", "      <td>AZUL</td>\n", "      <td>POLICARBONATO</td>\n", "      <td>POLICARBONATO</td>\n", "      <td>CASUAL</td>\n", "      <td>REDONDO</td>\n", "      <td>REDONDO</td>\n", "      <td>UNISSEX</td>\n", "      <td>CHILLI BEANS</td>\n", "      <td>OCULOS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>217026013170044</td>\n", "      <td>12498129000393</td>\n", "      <td>78144</td>\n", "      <td>748364</td>\n", "      <td>2025-03-01</td>\n", "      <td>214502</td>\n", "      <td>9421</td>\n", "      <td>1249812900039394212025-03-01</td>\n", "      <td>13170044</td>\n", "      <td>VENDA DE MERCADORIA</td>\n", "      <td>...</td>\n", "      <td>DEGRADE</td>\n", "      <td>MARROM</td>\n", "      <td>POLICARBONATO</td>\n", "      <td>POLICARBONATO</td>\n", "      <td>CASUAL</td>\n", "      <td>REDONDO</td>\n", "      <td>REDONDO</td>\n", "      <td>UNISSEX</td>\n", "      <td>CHILLI BEANS</td>\n", "      <td>OCULOS</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 62 columns</p>\n", "</div>"], "text/plain": ["    ID_Faturamento         id_loja  ID_Vendedor  ID_Cliente       data  \\\n", "0   96910220834170  36667417009863        76787     7774565 2025-03-01   \n", "1    5460502321150  15425148000696        78850    20112062 2025-03-01   \n", "2   67970100951576  53390229006953        77007    20111742 2025-03-01   \n", "3    1856605015822  55374963018718        78728    20110119 2025-03-01   \n", "4  217026013170044  12498129000393        78144      748364 2025-03-01   \n", "\n", "   id_produto  Documento                     DOC_UNICO  Transacao  \\\n", "0       17274       9015  3666741700986390152025-03-01     834170   \n", "1      214511       5225  1542514800069652252025-03-01    2321150   \n", "2      214506       4159  5339022900695341592025-03-01     951576   \n", "3      214503       1304  5537496301871813042025-03-01      15822   \n", "4      214502       9421  1249812900039394212025-03-01   13170044   \n", "\n", "     Natureza_Operacao  ...  Dim_Produtos.Cor1  \\\n", "0  VENDA DE MERCADORIA  ...            SORTIDO   \n", "1  VENDA DE MERCADORIA  ...              VERDE   \n", "2  VENDA DE MERCADORIA  ...             MARROM   \n", "3  VENDA DE MERCADORIA  ...            DEGRADE   \n", "4  VENDA DE MERCADORIA  ...            DEGRADE   \n", "\n", "                          Dim_Produtos.Cor2  Dim_Produtos.Material1  \\\n", "0  SORTIDO                                                 DIVERSOS   \n", "1  BRILHO                                                      TR90   \n", "2  VERDE                                                    ACETATO   \n", "3  AZUL                                               POLICARBONATO   \n", "4  MARROM                                             POLICARBONATO   \n", "\n", "                     Dim_Produtos.Material2  Dim_Produtos.Segmentacao  \\\n", "0  ESTOJO                                                         NaN   \n", "1  POLARIZADA                                                  CASUAL   \n", "2  NYLON                                                       CASUAL   \n", "3  POLICARBONATO                                               CASUAL   \n", "4  POLICARBONATO                                               CASUAL   \n", "\n", "   Dim_Produtos.<PERSON><PERSON>pe  Dim_Produtos.Formato Dim_Produtos.Sexo  \\\n", "0                 NaN            ACESSORIOS           UNISSEX   \n", "1            QUADRADO              QUADRADO         MASCULINO   \n", "2            QUADRADO              QUADRADO          FEMININO   \n", "3             REDONDO               REDONDO           UNISSEX   \n", "4             REDONDO               REDONDO           UNISSEX   \n", "\n", "   Dim_Produtos.Griffe  Dim_Produtos.GRUPO_CHILLI  \n", "0         CHILLI BEANS                 ACESSORIOS  \n", "1         CHILLI BEANS                     OCULOS  \n", "2              PREMIUM                     OCULOS  \n", "3         CHILLI BEANS                     OCULOS  \n", "4         CHILLI BEANS                     OCULOS  \n", "\n", "[5 rows x 62 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["#@title Carregamento dos dados limpos\n", "df = pd.read_csv(CLEAN_DATA, parse_dates=['data'])\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": 5, "id": "8aeaf031", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:26.487938Z", "iopub.status.busy": "2025-08-20T18:11:26.487938Z", "iopub.status.idle": "2025-08-20T18:11:26.537762Z", "shell.execute_reply": "2025-08-20T18:11:26.537242Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>valor</th>\n", "      <td>40291.0</td>\n", "      <td>26727.730486</td>\n", "      <td>31022.747888</td>\n", "      <td>-239900.0</td>\n", "      <td>2996.0</td>\n", "      <td>29998.0</td>\n", "      <td>39998.0</td>\n", "      <td>1105797.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>qtd</th>\n", "      <td>40291.0</td>\n", "      <td>1.024919</td>\n", "      <td>0.504607</td>\n", "      <td>-3.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>14.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         count          mean           std       min     25%      50%  \\\n", "valor  40291.0  26727.730486  31022.747888 -239900.0  2996.0  29998.0   \n", "qtd    40291.0      1.024919      0.504607      -3.0     1.0      1.0   \n", "\n", "           75%        max  \n", "valor  39998.0  1105797.0  \n", "qtd        1.0       14.0  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>freq</th>\n", "      <th>pct</th>\n", "    </tr>\n", "    <tr>\n", "      <th>uf</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SP</th>\n", "      <td>13916</td>\n", "      <td>36.608529</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MG</th>\n", "      <td>2763</td>\n", "      <td>7.268566</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PR</th>\n", "      <td>2584</td>\n", "      <td>6.797674</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SC</th>\n", "      <td>2490</td>\n", "      <td>6.550391</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RJ</th>\n", "      <td>2125</td>\n", "      <td>5.590193</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RS</th>\n", "      <td>1485</td>\n", "      <td>3.906558</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GO</th>\n", "      <td>1391</td>\n", "      <td>3.659274</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BA</th>\n", "      <td>1286</td>\n", "      <td>3.383053</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PA</th>\n", "      <td>1189</td>\n", "      <td>3.127877</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PE</th>\n", "      <td>1145</td>\n", "      <td>3.012127</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     freq        pct\n", "uf                  \n", "SP  13916  36.608529\n", "MG   2763   7.268566\n", "PR   2584   6.797674\n", "SC   2490   6.550391\n", "RJ   2125   5.590193\n", "RS   1485   3.906558\n", "GO   1391   3.659274\n", "BA   1286   3.383053\n", "PA   1189   3.127877\n", "PE   1145   3.012127"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>freq</th>\n", "      <th>pct</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cidade</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SÃO PAULO</th>\n", "      <td>4691</td>\n", "      <td>12.443961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GUARULHOS</th>\n", "      <td>1086</td>\n", "      <td>2.880866</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CURITIBA</th>\n", "      <td>951</td>\n", "      <td>2.522747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BRASÍLIA</th>\n", "      <td>936</td>\n", "      <td>2.482956</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RIO DE JANEIRO</th>\n", "      <td>899</td>\n", "      <td>2.384805</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SANTOS</th>\n", "      <td>633</td>\n", "      <td>1.679179</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FLORIANÓPOLIS</th>\n", "      <td>593</td>\n", "      <td>1.573069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GOIÂNIA</th>\n", "      <td>569</td>\n", "      <td>1.509404</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BELO HORIZONTE</th>\n", "      <td>517</td>\n", "      <td>1.371462</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MANAUS</th>\n", "      <td>459</td>\n", "      <td>1.217604</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                freq        pct\n", "cidade                         \n", "SÃO PAULO       4691  12.443961\n", "GUARULHOS       1086   2.880866\n", "CURITIBA         951   2.522747\n", "BRASÍLIA         936   2.482956\n", "RIO DE JANEIRO   899   2.384805\n", "SANTOS           633   1.679179\n", "FLORIANÓPOLIS    593   1.573069\n", "GOIÂNIA          569   1.509404\n", "BELO HORIZONTE   517   1.371462\n", "MANAUS           459   1.217604"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>freq</th>\n", "      <th>pct</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Tipo_PDV</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>LOJA</th>\n", "      <td>14210</td>\n", "      <td>37.009063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QUIOSQUE</th>\n", "      <td>7554</td>\n", "      <td>19.673924</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LOJA OTICO</th>\n", "      <td>5380</td>\n", "      <td>14.011876</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LOJA DE RUA HIBRIDO</th>\n", "      <td>4383</td>\n", "      <td>11.415252</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LOJA HIBRIDO</th>\n", "      <td>1914</td>\n", "      <td>4.984894</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QUIOSQUE HIBRIDO</th>\n", "      <td>1527</td>\n", "      <td>3.976977</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ECO CHILLI</th>\n", "      <td>1114</td>\n", "      <td>2.901344</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LOJA DE RUA</th>\n", "      <td>690</td>\n", "      <td>1.797062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QUIOSQUE OTICO</th>\n", "      <td>633</td>\n", "      <td>1.648609</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LOJA DE RUA OTICO</th>\n", "      <td>329</td>\n", "      <td>0.856860</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       freq        pct\n", "Tipo_PDV                              \n", "LOJA                  14210  37.009063\n", "QUIOSQUE               7554  19.673924\n", "LOJA OTICO             5380  14.011876\n", "LOJA DE RUA HIBRIDO    4383  11.415252\n", "LOJA HIBRIDO           1914   4.984894\n", "QUIOSQUE HIBRIDO       1527   3.976977\n", "ECO CHILLI             1114   2.901344\n", "LOJA DE RUA             690   1.797062\n", "QUIOSQUE OTICO          633   1.648609\n", "LOJA DE RUA OTICO       329   0.856860"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#@title Estatísticas descritivas e frequências\n", "num_cols = [c for c in ['valor','qtd'] if c in df.columns]\n", "cat_cols = [c for c in ['uf','cidade','Tipo_PDV'] if c in df.columns]\n", "display(df[num_cols].describe().T)\n", "for c in cat_cols:\n", "    display(pd.DataFrame({'freq': df[c].value_counts().head(10),\n", "                          'pct': df[c].value_counts(normalize=True).head(10)*100}))\n"]}, {"cell_type": "code", "execution_count": 6, "id": "12d36cd4", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:26.539771Z", "iopub.status.busy": "2025-08-20T18:11:26.539771Z", "iopub.status.idle": "2025-08-20T18:11:26.944339Z", "shell.execute_reply": "2025-08-20T18:11:26.943815Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Salvo: ..\\reports\\2025-08-15\\plots\\heatmap_corr.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#@title (EDA) Matriz de correlação (Spearman) e exportação\n", "num_cols_avail = [c for c in ['valor','qtd'] if c in df.columns]\n", "if len(num_cols_avail) >= 2:\n", "    corr = df[num_cols_avail].corr(method='spearman')\n", "    fig = plt.figure(figsize=(5,4))\n", "    sns.heatmap(corr, annot=True, cmap='coolwarm', vmin=-1, vmax=1)\n", "    plt.title('<PERSON><PERSON> (S<PERSON><PERSON>)')\n", "    plt.tight_layout()\n", "    try:\n", "        out_path = PLOTS_DIR / 'heatmap_corr.png'\n", "        fig.savefig(out_path, bbox_inches='tight')\n", "        print('Salvo:', out_path)\n", "    except Exception as e:\n", "        print('Aviso: não foi possível salvar heatmap_corr.png ->', e)\n", "    plt.show()\n", "else:\n", "    print('Variáveis numéricas insuficientes para correlação.')\n"]}, {"cell_type": "code", "execution_count": 7, "id": "f7c57709", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:26.947861Z", "iopub.status.busy": "2025-08-20T18:11:26.947861Z", "iopub.status.idle": "2025-08-20T18:11:28.171884Z", "shell.execute_reply": "2025-08-20T18:11:28.171884Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#@title Re<PERSON>ita por UF; Receita média por dia; Top 20 cidades\n", "# Receita por UF\n", "revenue_uf = df.groupby('uf')['valor'].sum().sort_values(ascending=False).reset_index()\n", "plt.figure(figsize=(12,5));\n", "sns.barplot(data=revenue_uf, x='uf', y='valor', palette='Blues_r');\n", "plt.title('<PERSON><PERSON><PERSON> por UF (centavos)'); plt.xticks(rotation=45); plt.tight_layout(); plt.show()\n", "# Re<PERSON>ita média por dia da semana\n", "df['dow'] = df['data'].dt.dayofweek\n", "dow_map = {0:'Seg',1:'<PERSON><PERSON>',2:'Qua',3:'Qui',4:'Sex',5:'<PERSON><PERSON><PERSON>',6:'Dom'}\n", "rev_dow = df.groupby('dow')['valor'].mean().rename(index=dow_map).reset_index()\n", "plt.figure(figsize=(8,4)); sns.barplot(data=rev_dow, x='dow', y='valor', palette='viridis');\n", "plt.title('<PERSON><PERSON><PERSON> por <PERSON> Semana'); plt.tight_layout(); plt.show()\n", "# Top 20 cidades\n", "top_cities = df.groupby(['uf','cidade'])['valor'].sum().reset_index().sort_values('valor', ascending=False).head(20)\n", "plt.figure(figsize=(10,8)); sns.barplot(data=top_cities, y='cidade', x='valor', hue='uf', dodge=False, palette='mako');\n", "plt.title('Top 20 Cidades por Receita'); plt.tight_layout(); plt.show()\n"]}, {"cell_type": "markdown", "id": "4b7ae40e", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "- Nulos; Outliers (MAD) + winsorização; Codificação (OneHot); Normalização (StandardScaler)"]}, {"cell_type": "code", "execution_count": 8, "id": "971bfc2c", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:28.174395Z", "iopub.status.busy": "2025-08-20T18:11:28.174395Z", "iopub.status.idle": "2025-08-20T18:11:28.203333Z", "shell.execute_reply": "2025-08-20T18:11:28.203333Z"}}, "outputs": [{"data": {"text/plain": ["(40291, 63)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#@title Limpeza de nulos e preparação\n", "df_pp = df.copy()\n", "if 'qtd' in df_pp.columns:\n", "    df_pp['qtd'] = df_pp['qtd'].fillna(0)\n", "df_pp = df_pp.dropna(subset=['data','id_loja'])\n", "df_pp.shape\n"]}, {"cell_type": "code", "execution_count": 9, "id": "bb305bc1", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:28.206436Z", "iopub.status.busy": "2025-08-20T18:11:28.205432Z", "iopub.status.idle": "2025-08-20T18:11:28.719172Z", "shell.execute_reply": "2025-08-20T18:11:28.719172Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>valor</th>\n", "      <td>40291.0</td>\n", "      <td>26727.730486</td>\n", "      <td>31022.747888</td>\n", "      <td>-239900.0</td>\n", "      <td>2996.0</td>\n", "      <td>29998.0</td>\n", "      <td>39998.0</td>\n", "      <td>1105797.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>valor_winsor</th>\n", "      <td>40291.0</td>\n", "      <td>25857.314646</td>\n", "      <td>23727.678871</td>\n", "      <td>-39998.0</td>\n", "      <td>2996.0</td>\n", "      <td>29998.0</td>\n", "      <td>39998.0</td>\n", "      <td>120173.8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                count          mean           std       min     25%      50%  \\\n", "valor         40291.0  26727.730486  31022.747888 -239900.0  2996.0  29998.0   \n", "valor_winsor  40291.0  25857.314646  23727.678871  -39998.0  2996.0  29998.0   \n", "\n", "                  75%        max  \n", "valor         39998.0  1105797.0  \n", "valor_winsor  39998.0   120173.8  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#@title Outliers (MAD) e winsorização\n", "import numpy as np\n", "def robust_z(x):\n", "    med = x.median(); mad = (x-med).abs().median()\n", "    if mad == 0: return pd.Series(np.zeros(len(x)), index=x.index)\n", "    return 0.6745*(x - med)/mad\n", "df_pp['rz'] = df_pp.groupby('id_loja')['valor'].transform(robust_z)\n", "outliers = df_pp[df_pp['rz'].abs() >= 3]\n", "cap_low = df_pp['valor'].quantile(0.01)\n", "cap_high = df_pp['valor'].quantile(0.99)\n", "df_pp['valor_winsor'] = df_pp['valor'].clip(lower=cap_low, upper=cap_high)\n", "df_pp[['valor','valor_winsor']].describe().T\n"]}, {"cell_type": "code", "execution_count": 10, "id": "8750c1f1", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:28.722182Z", "iopub.status.busy": "2025-08-20T18:11:28.721182Z", "iopub.status.idle": "2025-08-20T18:11:30.222000Z", "shell.execute_reply": "2025-08-20T18:11:30.222000Z"}}, "outputs": [{"data": {"text/plain": ["(40291, 1791)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["#@title Codificação e normalização\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "cat_cols = [c for c in ['uf','cidade','Tipo_PDV'] if c in df_pp.columns]\n", "num_cols = [c for c in ['valor_winsor','qtd'] if c in df_pp.columns]\n", "ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=False)\n", "X_cat = ohe.fit_transform(df_pp[cat_cols]) if cat_cols else np.empty((len(df_pp),0))\n", "scaler = StandardScaler()\n", "X_num = scaler.fit_transform(df_pp[num_cols]) if num_cols else np.empty((len(df_pp),0))\n", "X = np.hstack([X_num, X_cat]); X.shape\n"]}, {"cell_type": "markdown", "id": "d760b947", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON><PERSON> (H1–H3)\n", "H1: SP > demais <PERSON> (receita por loja)\n", "\n", "H2: <PERSON><PERSON> de semana ≠ dias ú<PERSON> (receita diária)\n", "\n", "H3: Cidades com múltiplas lojas > lojas únicas (total e por loja)"]}, {"cell_type": "code", "execution_count": 11, "id": "9f835462", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:30.224209Z", "iopub.status.busy": "2025-08-20T18:11:30.224209Z", "iopub.status.idle": "2025-08-20T18:11:30.279912Z", "shell.execute_reply": "2025-08-20T18:11:30.278904Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["H1 - <PERSON><PERSON><PERSON> (SP > outras UFs): U=535735.00 p=2.069e-40\n", "H2 - <PERSON><PERSON><PERSON> (fim de semana != dias ú<PERSON><PERSON>): U=4.00 p=0.8\n", "H3 - total (multi>single): U=698998.00 p=2.167e-207\n", "H3 - por loja (multi>single): U=558030.50 p=4.957e-68\n"]}], "source": ["#@title Testes H1–H3 (<PERSON><PERSON>)\n", "from scipy import stats\n", "# H1\n", "sp = df.groupby(['uf','id_loja'])['valor'].sum().reset_index()\n", "sp['valor_por_loja'] = sp['valor']\n", "sp_vals = sp.loc[sp['uf']=='SP','valor_por_loja']\n", "others = sp.loc[sp['uf']!='SP','valor_por_loja']\n", "if len(sp_vals)>0 and len(others)>0:\n", "    stat1, p1 = stats.mannwhitneyu(sp_vals, others, alternative='greater')\n", "    print(f'H1 - <PERSON><PERSON><PERSON> (SP > outras UFs): U={stat1:.2f} p={p1:.4g}')\n", "# H2\n", "df['is_weekend'] = df['data'].dt.dayofweek >= 5\n", "dw_daily = df.groupby(['data','is_weekend'])['valor'].sum().reset_index()\n", "weekend = dw_daily.loc[dw_daily['is_weekend']==True,'valor']\n", "weekday = dw_daily.loc[dw_daily['is_weekend']==False,'valor']\n", "if len(weekend)>0 and len(weekday)>0:\n", "    stat2, p2 = stats.mannwhitneyu(weekend, weekday, alternative='two-sided')\n", "    print(f'H2 - <PERSON><PERSON><PERSON> (fim de semana != dias <PERSON><PERSON><PERSON>): U={stat2:.2f} p={p2:.4g}')\n", "# H3\n", "city_store = df.groupby(['uf','cidade'])['id_loja'].nunique().reset_index(name='n_lojas')\n", "city_rev = df.groupby(['uf','cidade'])['valor'].sum().reset_index(name='receita_total')\n", "city = city_store.merge(city_rev, on=['uf','cidade'])\n", "city['receita_por_loja'] = city['receita_total']/city['n_lojas']\n", "multi = city.loc[city['n_lojas']>1]\n", "single = city.loc[city['n_lojas']==1]\n", "if len(multi)>0 and len(single)>0:\n", "    s3a, p3a = stats.mannwhitneyu(multi['receita_total'], single['receita_total'], alternative='greater')\n", "    s3b, p3b = stats.mannwhitneyu(multi['receita_por_loja'], single['receita_por_loja'], alternative='greater')\n", "    print(f'H3 - total (multi>single): U={s3a:.2f} p={p3a:.4g}')\n", "    print(f'H3 - por loja (multi>single): U={s3b:.2f} p={p3b:.4g}')\n"]}, {"cell_type": "code", "execution_count": 12, "id": "0ae49916", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:30.281911Z", "iopub.status.busy": "2025-08-20T18:11:30.281911Z", "iopub.status.idle": "2025-08-20T18:11:31.180910Z", "shell.execute_reply": "2025-08-20T18:11:31.180910Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SP por loja: <PERSON><PERSON><PERSON><PERSON><PERSON> p=1.409e-24\n", "statsmodels ausente; pulando QQ-plot.\n", "Outras UFs por loja: <PERSON><PERSON><PERSON><PERSON><PERSON> p=7.467e-55\n", "statsmodels ausente; pulando QQ-plot.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["H1 - Cliff's delta: 0.416\n", "H2 - Cliff's delta: 0.333\n"]}, {"name": "stdout", "output_type": "stream", "text": ["H3 - Cliff's delta (total): 0.869\n"]}, {"name": "stdout", "output_type": "stream", "text": ["H3 - Cliff's delta (por loja): 0.492\n"]}], "source": ["#@title Diagnóstico de distribuição e tamanho de efeito (delta de Cliff)\n", "import scipy.stats as ss\n", "try:\n", "    import statsmodels.api as sm\n", "except Exception:\n", "    sm = None\n", "def cliffs_delta(x, y):\n", "    x = pd.Series(x).dropna().values; y = pd.Series(y).dropna().values\n", "    gt = sum(xx > yy for xx in x for yy in y)\n", "    lt = sum(xx < yy for xx in x for yy in y)\n", "    n = len(x)*len(y)\n", "    return (gt - lt)/n if n>0 else float('nan')\n", "samples = {\n", "    'SP por loja': sp_vals,\n", "    'Outras UFs por loja': others,\n", "    'Re<PERSON>ita diária fim de semana': weekend,\n", "    'Re<PERSON>ita diária dia <PERSON>': weekday\n", "}\n", "for name, s in samples.items():\n", "    s = pd.Series(s).dropna()\n", "    if len(s) > 8:\n", "        stat, p = ss.shapiro(s.sample(min(5000, len(s)), random_state=42))\n", "        print(f'{name}: <PERSON><PERSON><PERSON><PERSON><PERSON> p={p:.4g}')\n", "        \n", "        if sm is not None:\n", "            sm.qqplot(s, line='s'); plt.title(f'QQ-plot: {name}'); plt.show()\n", "        else:\n", "            print('statsmodels ausente; pulando QQ-plot.')\n", "# Cliff's delta\n", "if len(sp_vals)>0 and len(others)>0:\n", "    print(f\"H1 - Cliff's delta: {cliffs_delta(sp_vals, others):.3f}\")\n", "if len(weekend)>0 and len(weekday)>0:\n", "    print(f\"H2 - Cliff's delta: {cliffs_delta(weekend, weekday):.3f}\")\n", "if len(multi)>0 and len(single)>0:\n", "    print(f\"H3 - Cliff's delta (total): {cliffs_delta(multi['receita_total'], single['receita_total']):.3f}\")\n", "    print(f\"H3 - Cliff's delta (por loja): {cliffs_delta(multi['receita_por_loja'], single['receita_por_loja']):.3f}\")\n"]}, {"cell_type": "code", "execution_count": 13, "id": "eb31a11c", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:31.184424Z", "iopub.status.busy": "2025-08-20T18:11:31.182918Z", "iopub.status.idle": "2025-08-20T18:11:31.190809Z", "shell.execute_reply": "2025-08-20T18:11:31.190809Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["H1: bruto=2.069e-40 | <PERSON><PERSON><PERSON><PERSON>(Holm)=4.138e-40\n", "H2: bruto=0.8 | a<PERSON><PERSON>o(Holm)=0.8\n", "H3 (ma<PERSON> p): bruto=4.957e-68 | a<PERSON><PERSON><PERSON>(Ho<PERSON>)=1.487e-67\n"]}], "source": ["#@title <PERSON><PERSON><PERSON> (p-valores ajustados)\n", "import numpy as np\n", "pvals = []\n", "labels = []\n", "if 'p1' in globals(): pvals.append(p1); labels.append('H1')\n", "if 'p2' in globals(): pvals.append(p2); labels.append('H2')\n", "if 'p3a' in globals() and 'p3b' in globals(): pvals.append(max(p3a, p3b)); labels.append('H3 (maior p)')\n", "def holm_bonferroni(ps):\n", "    m = len(ps); order = np.argsort(ps); adj = np.empty(m)\n", "    for i, idx in enumerate(order):\n", "        adj[idx] = min((m - i) * ps[idx], 1.0)\n", "    return adj\n", "if pvals:\n", "    adj = holm_bonferroni(pvals)\n", "    for lab, raw, ap in zip(labels, pvals, adj):\n", "        print(f'{lab}: bruto={raw:.4g} | ajustado(Holm)={ap:.4g}')\n"]}, {"cell_type": "markdown", "id": "a56ce683", "metadata": {}, "source": ["## 4. Integração com Resultados Existentes\n", "- Referência aos artefatos em `reports/2025-08-15/` (16 PNGs, 18 CSVs)"]}, {"cell_type": "code", "execution_count": 14, "id": "4c34805c", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:31.192815Z", "iopub.status.busy": "2025-08-20T18:11:31.192815Z", "iopub.status.idle": "2025-08-20T18:11:31.222522Z", "shell.execute_reply": "2025-08-20T18:11:31.222522Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exemplos CSV: ['additional_stats.csv', 'baseline_metrics.csv', 'city_revenue_rankings.csv', 'corr_matrix.csv', 'detailed_geographic_summary.csv']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>periodo</th>\n", "      <th>lojas</th>\n", "      <th>receita_total_centavos</th>\n", "      <th>n_plots_png</th>\n", "      <th>lojas_com_baseline</th>\n", "      <th>n_lojas_treinadas</th>\n", "      <th>baseline_mae_mediana</th>\n", "      <th>MAE_modelo</th>\n", "      <th>RMSE_modelo</th>\n", "      <th>modelo_mae_mediana</th>\n", "      <th>short_series_mode</th>\n", "      <th>days_available</th>\n", "      <th>total_cities_analyzed</th>\n", "      <th>high_performance_cities</th>\n", "      <th>top_city_by_revenue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-03-01-&gt;2025-03-05</td>\n", "      <td>928</td>\n", "      <td>1076886989</td>\n", "      <td>12</td>\n", "      <td>712</td>\n", "      <td>899</td>\n", "      <td>118243.5</td>\n", "      <td>3751.620139</td>\n", "      <td>9602.417982</td>\n", "      <td>3751.620139</td>\n", "      <td>True</td>\n", "      <td>5</td>\n", "      <td>1330</td>\n", "      <td>669</td>\n", "      <td>SÃO PAULO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  periodo  lojas  receita_total_centavos  n_plots_png  \\\n", "0  2025-03-01->2025-03-05    928              1076886989           12   \n", "\n", "   lojas_com_baseline  n_lojas_treinadas  baseline_mae_mediana   MAE_modelo  \\\n", "0                 712                899              118243.5  3751.620139   \n", "\n", "   RMSE_modelo  modelo_mae_mediana  short_series_mode  days_available  \\\n", "0  9602.417982         3751.620139               True               5   \n", "\n", "   total_cities_analyzed  high_performance_cities top_city_by_revenue  \n", "0                   1330                      669           SÃO PAULO  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["#@title Leitura de tabelas geradas (exemplos)\n", "import os\n", "tables = sorted([p for p in os.listdir(TABLES_DIR) if p.endswith('.csv')])[:5]\n", "print('Exemplos CSV:', tables)\n", "if (TABLES_DIR / 'summary.csv').exists():\n", "    example = pd.read_csv(TABLES_DIR / 'summary.csv')\n", "    display(example.head())\n"]}, {"cell_type": "markdown", "id": "e74dd0df", "metadata": {}, "source": ["## 5. Validação automática e checklist final\n", "Valida PNG=16 e CSV=18 e lista diretórios vazios."]}, {"cell_type": "code", "execution_count": 15, "id": "42281d31", "metadata": {"execution": {"iopub.execute_input": "2025-08-20T18:11:31.224530Z", "iopub.status.busy": "2025-08-20T18:11:31.224530Z", "iopub.status.idle": "2025-08-20T18:11:31.234814Z", "shell.execute_reply": "2025-08-20T18:11:31.234309Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PNG files: 16  (esperado: 16)\n", "CSV files: 18  (esperado: 18)\n", "Diretórios vazios: ['data']\n"]}], "source": ["#@title Validação automática dos artefatos e diretórios vazios\n", "import os\n", "pngs = [p for p in os.listdir(PLOTS_DIR) if p.endswith('.png')]\n", "csvs = [p for p in os.listdir(TABLES_DIR) if p.endswith('.csv')]\n", "print('PNG files:', len(pngs), ' (esperado: 16)')\n", "print('CSV files:', len(csvs), ' (esperado: 18)')\n", "empty_dirs = []\n", "for root, dirs, files in os.walk('.'):\n", "    for d in dirs:\n", "        full = Path(root) / d\n", "        try:\n", "            if len(list((full).iterdir())) == 0:\n", "                empty_dirs.append(str(full))\n", "        except Exception:\n", "            pass\n", "print('<PERSON><PERSON><PERSON><PERSON><PERSON> vazios:', empty_dirs if empty_dirs else 'nenhum')\n"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}