import nbformat as nbf
from pathlib import Path

def md(text):
    cell = nbf.v4.new_markdown_cell(text)
    return cell

def code(text):
    cell = nbf.v4.new_code_cell(text)
    return cell

BASE = Path(__file__).resolve().parents[1]
NB_PATH = BASE / 'notebooks' / 'chilli_beans_analysis.ipynb'

nb = nbf.v4.new_notebook()

# 0. Header
nb.cells.append(md(
    "# Análise de Vendas - Chilli Beans (Colab)\n\n> Notebook preparado para execução no Google Colab. Integra os resultados em `reports/2025-08-15/`."
))

# 0.1 Paths & imports
nb.cells.append(code(
    "#@title Preparação do Ambiente\n"
    "from pathlib import Path\n"
    "import pandas as pd, numpy as np\n"
    "import matplotlib.pyplot as plt, seaborn as sns\n"
    "from warnings import filterwarnings; filterwarnings('ignore')\n"
    "BASE_DIR = Path('.')\n"
    "# Fallback quando executado a partir da pasta notebooks\n"
    "if not (BASE_DIR / 'data' / 'clean' / 'cleaned.csv').exists():\n"
    "    BASE_DIR = Path('..')\n"
    "REPORTS_DIR = BASE_DIR / 'reports' / '2025-08-15'\n"
    "PLOTS_DIR = REPORTS_DIR / 'plots'\n"
    "TABLES_DIR = REPORTS_DIR / 'tables'\n"
    "CLEAN_DATA = BASE_DIR / 'data' / 'clean' / 'cleaned.csv'\n"
    "print('PLOTS_DIR:', PLOTS_DIR)\n"
    "print('TABLES_DIR:', TABLES_DIR)\n"
    "print('CLEAN_DATA:', CLEAN_DATA)\n"
    "\n"
    "# Atualiza sys.path para permitir imports do pacote src/\n"
    "import sys\n"
    "PROJ_ROOT = BASE_DIR.resolve()\n"
    "SRC_DIR = PROJ_ROOT / 'src'\n"
    "for p in [str(PROJ_ROOT), str(SRC_DIR)]:\n"
    "    if p not in sys.path:\n"
    "        sys.path.insert(0, p)\n"
    "print('sys.path atualizado com', SRC_DIR)\n"
))

# 0.15 Teste de imports do pacote src
nb.cells.append(code(
    "#@title Teste de imports do pacote src\n"
    "mods = ['analysis_stats','baselines','features','geo_utils','io_utils','metrics']\n"
    "for m in mods:\n"
    "    try:\n"
    "        __import__(f'src.{m}')\n"
    "        print(f'OK: src.{m}')\n"
    "    except Exception as e:\n"
    "        print(f'FALHA: src.{m} -> {e}')\n"
))

# 0.2 Reproducibility versions
nb.cells.append(code(
    "#@title (Opcional) Fixar versões para reprodutibilidade (Colab)\n"
    "DO_PIP = False\n"
    "if DO_PIP:\n"
    "    import IPython\n"
    "    IPython.get_ipython().run_line_magic('pip', 'install -q pandas>=1.5.0 numpy>=1.21.0 scipy>=1.9.0 scikit-learn>=1.1.0 seaborn>=0.12.0 matplotlib>=3.5.0 statsmodels>=0.13.0')\n"
    "import numpy as np\n"
    "RANDOM_SEED = 42\n"
    "np.random.seed(RANDOM_SEED)\n"
    "print('Random seed set to', RANDOM_SEED)\n"
))

# 1. EDA
nb.cells.append(md(
    "## 1. Exploração de Dados (EDA)\n- Variáveis numéricas: `valor`, `qtd`\n- Categóricas: `uf`, `cidade`, `Tipo_PDV`\n- Gráficos: Receita por UF; Receita média por dia da semana; Top 20 cidades; Correlação Spearman (valor×qtd)"
))

nb.cells.append(code(
    "#@title Carregamento dos dados limpos\n"
    "df = pd.read_csv(CLEAN_DATA, parse_dates=['data'])\n"
    "df.head()\n"
))

nb.cells.append(code(
    "#@title Estatísticas descritivas e frequências\n"
    "num_cols = [c for c in ['valor','qtd'] if c in df.columns]\n"
    "cat_cols = [c for c in ['uf','cidade','Tipo_PDV'] if c in df.columns]\n"
    "display(df[num_cols].describe().T)\n"
    "for c in cat_cols:\n"
    "    display(pd.DataFrame({'freq': df[c].value_counts().head(10),\n"
    "                          'pct': df[c].value_counts(normalize=True).head(10)*100}))\n"
))

nb.cells.append(code(
    "#@title (EDA) Matriz de correlação (Spearman) e exportação\n"
    "num_cols_avail = [c for c in ['valor','qtd'] if c in df.columns]\n"
    "if len(num_cols_avail) >= 2:\n"
    "    corr = df[num_cols_avail].corr(method='spearman')\n"
    "    fig = plt.figure(figsize=(5,4))\n"
    "    sns.heatmap(corr, annot=True, cmap='coolwarm', vmin=-1, vmax=1)\n"
    "    plt.title('Matriz de Correlação (Spearman)')\n"
    "    plt.tight_layout()\n"
    "    try:\n"
    "        out_path = PLOTS_DIR / 'heatmap_corr.png'\n"
    "        fig.savefig(out_path, bbox_inches='tight')\n"
    "        print('Salvo:', out_path)\n"
    "    except Exception as e:\n"
    "        print('Aviso: não foi possível salvar heatmap_corr.png ->', e)\n"
    "    plt.show()\n"
    "else:\n"
    "    print('Variáveis numéricas insuficientes para correlação.')\n"
))

nb.cells.append(code(
    "#@title Receita por UF; Receita média por dia; Top 20 cidades\n"
    "# Receita por UF\n"
    "revenue_uf = df.groupby('uf')['valor'].sum().sort_values(ascending=False).reset_index()\n"
    "plt.figure(figsize=(12,5));\n"
    "sns.barplot(data=revenue_uf, x='uf', y='valor', palette='Blues_r');\n"
    "plt.title('Receita por UF (centavos)'); plt.xticks(rotation=45); plt.tight_layout(); plt.show()\n"
    "# Receita média por dia da semana\n"
    "df['dow'] = df['data'].dt.dayofweek\n"
    "dow_map = {0:'Seg',1:'Ter',2:'Qua',3:'Qui',4:'Sex',5:'Sáb',6:'Dom'}\n"
    "rev_dow = df.groupby('dow')['valor'].mean().rename(index=dow_map).reset_index()\n"
    "plt.figure(figsize=(8,4)); sns.barplot(data=rev_dow, x='dow', y='valor', palette='viridis');\n"
    "plt.title('Receita Média por Dia da Semana'); plt.tight_layout(); plt.show()\n"
    "# Top 20 cidades\n"
    "top_cities = df.groupby(['uf','cidade'])['valor'].sum().reset_index().sort_values('valor', ascending=False).head(20)\n"
    "plt.figure(figsize=(10,8)); sns.barplot(data=top_cities, y='cidade', x='valor', hue='uf', dodge=False, palette='mako');\n"
    "plt.title('Top 20 Cidades por Receita'); plt.tight_layout(); plt.show()\n"
))

# 2. Preprocess
nb.cells.append(md(
    "## 2. Pré-processamento\n- Nulos; Outliers (MAD) + winsorização; Codificação (OneHot); Normalização (StandardScaler)"
))

nb.cells.append(code(
    "#@title Limpeza de nulos e preparação\n"
    "df_pp = df.copy()\n"
    "if 'qtd' in df_pp.columns:\n"
    "    df_pp['qtd'] = df_pp['qtd'].fillna(0)\n"
    "df_pp = df_pp.dropna(subset=['data','id_loja'])\n"
    "df_pp.shape\n"
))

nb.cells.append(code(
    "#@title Outliers (MAD) e winsorização\n"
    "import numpy as np\n"
    "def robust_z(x):\n"
    "    med = x.median(); mad = (x-med).abs().median()\n"
    "    if mad == 0: return pd.Series(np.zeros(len(x)), index=x.index)\n"
    "    return 0.6745*(x - med)/mad\n"
    "df_pp['rz'] = df_pp.groupby('id_loja')['valor'].transform(robust_z)\n"
    "outliers = df_pp[df_pp['rz'].abs() >= 3]\n"
    "cap_low = df_pp['valor'].quantile(0.01)\n"
    "cap_high = df_pp['valor'].quantile(0.99)\n"
    "df_pp['valor_winsor'] = df_pp['valor'].clip(lower=cap_low, upper=cap_high)\n"
    "df_pp[['valor','valor_winsor']].describe().T\n"
))

nb.cells.append(code(
    "#@title Codificação e normalização\n"
    "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n"
    "cat_cols = [c for c in ['uf','cidade','Tipo_PDV'] if c in df_pp.columns]\n"
    "num_cols = [c for c in ['valor_winsor','qtd'] if c in df_pp.columns]\n"
    "ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=False)\n"
    "X_cat = ohe.fit_transform(df_pp[cat_cols]) if cat_cols else np.empty((len(df_pp),0))\n"
    "scaler = StandardScaler()\n"
    "X_num = scaler.fit_transform(df_pp[num_cols]) if num_cols else np.empty((len(df_pp),0))\n"
    "X = np.hstack([X_num, X_cat]); X.shape\n"
))

# 3. Hypotheses
nb.cells.append(md(
    "## 3. Hipóteses (H1–H3)\nH1: SP > demais UFs (receita por loja)\n\nH2: Fim de semana ≠ dias úteis (receita diária)\n\nH3: Cidades com múltiplas lojas > lojas únicas (total e por loja)"
))

nb.cells.append(code(
    "#@title Testes H1–H3 (Mann–Whitney)\n"
    "from scipy import stats\n"
    "# H1\n"
    "sp = df.groupby(['uf','id_loja'])['valor'].sum().reset_index()\n"
    "sp['valor_por_loja'] = sp['valor']\n"
    "sp_vals = sp.loc[sp['uf']=='SP','valor_por_loja']\n"
    "others = sp.loc[sp['uf']!='SP','valor_por_loja']\n"
    "if len(sp_vals)>0 and len(others)>0:\n"
    "    stat1, p1 = stats.mannwhitneyu(sp_vals, others, alternative='greater')\n"
    "    print(f'H1 - Mann-Whitney (SP > outras UFs): U={stat1:.2f} p={p1:.4g}')\n"
    "# H2\n"
    "df['is_weekend'] = df['data'].dt.dayofweek >= 5\n"
    "dw_daily = df.groupby(['data','is_weekend'])['valor'].sum().reset_index()\n"
    "weekend = dw_daily.loc[dw_daily['is_weekend']==True,'valor']\n"
    "weekday = dw_daily.loc[dw_daily['is_weekend']==False,'valor']\n"
    "if len(weekend)>0 and len(weekday)>0:\n"
    "    stat2, p2 = stats.mannwhitneyu(weekend, weekday, alternative='two-sided')\n"
    "    print(f'H2 - Mann-Whitney (fim de semana != dias úteis): U={stat2:.2f} p={p2:.4g}')\n"
    "# H3\n"
    "city_store = df.groupby(['uf','cidade'])['id_loja'].nunique().reset_index(name='n_lojas')\n"
    "city_rev = df.groupby(['uf','cidade'])['valor'].sum().reset_index(name='receita_total')\n"
    "city = city_store.merge(city_rev, on=['uf','cidade'])\n"
    "city['receita_por_loja'] = city['receita_total']/city['n_lojas']\n"
    "multi = city.loc[city['n_lojas']>1]\n"
    "single = city.loc[city['n_lojas']==1]\n"
    "if len(multi)>0 and len(single)>0:\n"
    "    s3a, p3a = stats.mannwhitneyu(multi['receita_total'], single['receita_total'], alternative='greater')\n"
    "    s3b, p3b = stats.mannwhitneyu(multi['receita_por_loja'], single['receita_por_loja'], alternative='greater')\n"
    "    print(f'H3 - total (multi>single): U={s3a:.2f} p={p3a:.4g}')\n"
    "    print(f'H3 - por loja (multi>single): U={s3b:.2f} p={p3b:.4g}')\n"
))

nb.cells.append(code(
    "#@title Diagnóstico de distribuição e tamanho de efeito (delta de Cliff)\n"
    "import scipy.stats as ss\n"
    "try:\n"
    "    import statsmodels.api as sm\n"
    "except Exception:\n"
    "    sm = None\n"
    "def cliffs_delta(x, y):\n"
    "    x = pd.Series(x).dropna().values; y = pd.Series(y).dropna().values\n"
    "    gt = sum(xx > yy for xx in x for yy in y)\n"
    "    lt = sum(xx < yy for xx in x for yy in y)\n"
    "    n = len(x)*len(y)\n"
    "    return (gt - lt)/n if n>0 else float('nan')\n"
    "samples = {\n"
    "    'SP por loja': sp_vals,\n"
    "    'Outras UFs por loja': others,\n"
    "    'Receita diária fim de semana': weekend,\n"
    "    'Receita diária dia útil': weekday\n"
    "}\n"
    "for name, s in samples.items():\n"
    "    s = pd.Series(s).dropna()\n"
    "    if len(s) > 8:\n"
    "        stat, p = ss.shapiro(s.sample(min(5000, len(s)), random_state=42))\n"
    "        print(f'{name}: Shapiro-Wilk p={p:.4g}')\n"
    "        \n"
    "        if sm is not None:\n"
    "            sm.qqplot(s, line='s'); plt.title(f'QQ-plot: {name}'); plt.show()\n"
    "        else:\n"
    "            print('statsmodels ausente; pulando QQ-plot.')\n"
    "# Cliff's delta\n"
    "if len(sp_vals)>0 and len(others)>0:\n"
    "    print(f\"H1 - Cliff's delta: {cliffs_delta(sp_vals, others):.3f}\")\n"
    "if len(weekend)>0 and len(weekday)>0:\n"
    "    print(f\"H2 - Cliff's delta: {cliffs_delta(weekend, weekday):.3f}\")\n"
    "if len(multi)>0 and len(single)>0:\n"
    "    print(f\"H3 - Cliff's delta (total): {cliffs_delta(multi['receita_total'], single['receita_total']):.3f}\")\n"
    "    print(f\"H3 - Cliff's delta (por loja): {cliffs_delta(multi['receita_por_loja'], single['receita_por_loja']):.3f}\")\n"
))

nb.cells.append(code(
    "#@title Correção Holm-Bonferroni (p-valores ajustados)\n"
    "import numpy as np\n"
    "pvals = []\n"
    "labels = []\n"
    "if 'p1' in globals(): pvals.append(p1); labels.append('H1')\n"
    "if 'p2' in globals(): pvals.append(p2); labels.append('H2')\n"
    "if 'p3a' in globals() and 'p3b' in globals(): pvals.append(max(p3a, p3b)); labels.append('H3 (maior p)')\n"
    "def holm_bonferroni(ps):\n"
    "    m = len(ps); order = np.argsort(ps); adj = np.empty(m)\n"
    "    for i, idx in enumerate(order):\n"
    "        adj[idx] = min((m - i) * ps[idx], 1.0)\n"
    "    return adj\n"
    "if pvals:\n"
    "    adj = holm_bonferroni(pvals)\n"
    "    for lab, raw, ap in zip(labels, pvals, adj):\n"
    "        print(f'{lab}: bruto={raw:.4g} | ajustado(Holm)={ap:.4g}')\n"
))

# 4. Integration
nb.cells.append(md(
    "## 4. Integração com Resultados Existentes\n- Referência aos artefatos em `reports/2025-08-15/` (16 PNGs, 18 CSVs)"
))

nb.cells.append(code(
    "#@title Leitura de tabelas geradas (exemplos)\n"
    "import os\n"
    "tables = sorted([p for p in os.listdir(TABLES_DIR) if p.endswith('.csv')])[:5]\n"
    "print('Exemplos CSV:', tables)\n"
    "if (TABLES_DIR / 'summary.csv').exists():\n"
    "    example = pd.read_csv(TABLES_DIR / 'summary.csv')\n"
    "    display(example.head())\n"
))

# 5. Validation
nb.cells.append(md(
    "## 5. Validação automática e checklist final\nValida PNG=16 e CSV=18 e lista diretórios vazios."
))

nb.cells.append(code(
    "#@title Validação automática dos artefatos e diretórios vazios\n"
    "import os\n"
    "pngs = [p for p in os.listdir(PLOTS_DIR) if p.endswith('.png')]\n"
    "csvs = [p for p in os.listdir(TABLES_DIR) if p.endswith('.csv')]\n"
    "print('PNG files:', len(pngs), ' (esperado: 16)')\n"
    "print('CSV files:', len(csvs), ' (esperado: 18)')\n"
    "empty_dirs = []\n"
    "for root, dirs, files in os.walk('.'):\n"
    "    for d in dirs:\n"
    "        full = Path(root) / d\n"
    "        try:\n"
    "            if len(list((full).iterdir())) == 0:\n"
    "                empty_dirs.append(str(full))\n"
    "        except Exception:\n"
    "            pass\n"
    "print('Diretórios vazios:', empty_dirs if empty_dirs else 'nenhum')\n"
))

# Write notebook
NB_PATH.parent.mkdir(parents=True, exist_ok=True)
with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Notebook rebuilt at', NB_PATH)

