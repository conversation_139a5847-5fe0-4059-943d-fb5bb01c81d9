{"cells": [{"cell_type": "code", "execution_count": null, "id": "f7a8298b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "from sklearn.preprocessing import MinMaxScaler\n", "\n", "# --- 1. FUNÇÃO AUXILIAR PARA CÁLCULO DOS BINS ---\n", "\n", "def freedman_diaconis_bins(data):\n", "    \"\"\"Calcula o número ótimo de bins para um histograma.\"\"\"\n", "    q75, q25 = np.percentile(data, [75, 25])\n", "    iqr = q75 - q25\n", "    # Evita divisão por zero se os dados forem constantes\n", "    if iqr == 0:\n", "        return 15 # <PERSON><PERSON><PERSON> um valor padrão\n", "    \n", "    bin_width = 2 * iqr * (len(data) ** (-1/3))\n", "    # Garante que a largura do bin não seja zero\n", "    if bin_width == 0:\n", "        return 15\n", "        \n", "    bins = int(np.ceil((data.max() - data.min()) / bin_width))\n", "    \n", "    # Limita o número de bins para evitar gráficos muito poluídos\n", "    return min(bins, 100)\n", "\n", "# --- 2. <PERSON>RREGAMENT<PERSON> E PREPARAÇÃO DOS DADOS ---\n", "\n", "# Carrega o seu dataset\n", "try:\n", "    df = pd.read_csv('cleaned_filtered.csv')\n", "    print(\"Arquivo 'cleaned_filtered.csv' carregado com sucesso.\")\n", "except FileNotFoundError:\n", "    print(\"Erro: Arquivo 'cleaned_filtered.csv' não encontrado. Certifique-se de que ele está no mesmo diretório que o script.\")\n", "    exit()\n", "\n", "# Define as colunas de interesse com os nomes corretos\n", "variables = ['qtd', 'valor', 'Desconto']\n", "\n", "# Garante que as colunas são numéricas, tratando possíveis erros\n", "for col in variables:\n", "    df[col] = pd.to_numeric(df[col], errors='coerce')\n", "\n", "# Remove linhas onde os valores possam ser nulos após a conversão\n", "df.dropna(subset=variables, inplace=True)\n", "\n", "\n", "# --- 3. APLICAÇÃO DO ESCALONAMENTO (NORMALIZAÇÃO) ---\n", "\n", "print(\"\\nAplicando Normalização (Min-Max Scaler)...\")\n", "scaler = MinMaxScaler()\n", "\n", "# Cria um novo DataFrame para os dados normalizados\n", "df_scaled = pd.DataFrame(\n", "    scaler.fit_transform(df[variables]),\n", "    columns=[f'{col}_norm' for col in variables]\n", ")\n", "print(\"Dados normalizados com sucesso.\")\n", "\n", "\n", "# --- 4. GERAÇÃO E SALVAMENTO DOS HISTOGRAMAS ---\n", "\n", "# Cria o diretório para salvar as figuras, se não existir\n", "FIGDIR = \"figuras_reais_normalizadas\"\n", "if not os.path.exists(FIGDIR):\n", "    os.makedirs(FIGDIR)\n", "\n", "print(f\"\\nGerando e salvando histogramas na pasta '{FIGDIR}'...\")\n", "\n", "# Itera sobre cada coluna do DataFrame normalizado\n", "for col in df_scaled.columns:\n", "    # Extrai os dados da coluna\n", "    s = df_scaled[col].dropna().values\n", "    \n", "    # Calcula média e mediana\n", "    mean_, med_ = float(np.mean(s)), float(np.median(s))\n", "    \n", "    # Calcula o número de bins\n", "    bins = freedman_diaconis_bins(s)\n", "    \n", "    # Cria a figura do gráfico\n", "    plt.figure(figsize=(8, 5))\n", "    plt.hist(s, bins=bins, edgecolor=\"black\", alpha=0.7)\n", "    \n", "    # Adiciona título e rótulos\n", "    plt.title(f\"Histograma — {col}\")\n", "    plt.xlabel(col)\n", "    plt.ylabel(\"Frequência\")\n", "    \n", "    # Adiciona linhas verticais para média e mediana\n", "    plt.axvline(mean_, color='red', linestyle=\"--\", label=f\"Média={mean_:.3f}\")\n", "    plt.axvline(med_, color='green', linestyle=\":\", label=f\"Mediana={med_:.3f}\")\n", "    \n", "    # Adiciona legenda e grade para melhor visualização\n", "    plt.legend()\n", "    plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "    plt.tight_layout()\n", "    \n", "    # Salva a figura no diretório\n", "    path = os.path.join(FIGDIR, f\"hist_{col}.png\")\n", "    plt.savefig(path, dpi=150)\n", "    plt.close() # <PERSON>cha a figura para liberar memória\n", "    \n", "    print(f\"Figura salva: {path}\")\n", "\n", "print(\"\\nProcesso concluído!\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}