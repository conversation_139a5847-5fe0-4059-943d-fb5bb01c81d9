"""Métricas de séries temporais para avaliação de baselines e modelos
Implementa métricas de avaliação para comparar previsões com valores reais."""
from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Iterable


def wape(y_true: Iterable[float], y_pred: Iterable[float]) -> float:
    # Weighted Absolute Percentage Error: soma(|real - previsto|) / soma(|real|)
    y_true = np.asarray(y_true)  # Converte entrada para numpy array
    y_pred = np.asarray(y_pred)  # Garante consistência de tipos
    denom = np.abs(y_true).sum() # Calcula denominador (soma dos valores reais)
    if denom == 0:               # Evita divisão por zero
        return np.nan
    return np.abs(y_true - y_pred).sum() / denom


def smape(y_true: Iterable[float], y_pred: Iterable[float]) -> float:
    # Symmetric Mean Absolute Percentage Error: média(2 * |real - previsto| / (|real| + |previsto|))
    y_true = np.asarray(y_true)  # Converte entrada para numpy array
    y_pred = np.asarray(y_pred)  # Garante consistência de tipos
    denom = (np.abs(y_true) + np.abs(y_pred))  # Calcula denominador
    denom[denom == 0] = 1  # Substitui zeros por 1 para evitar divisão por zero
    return (2 * np.abs(y_true - y_pred) / denom).mean()


def mae(y_true: Iterable[float], y_pred: Iterable[float]) -> float:
    # Mean Absolute Error: média(|real - previsto|)
    y_true = np.asarray(y_true)  # Converte entrada para numpy array
    y_pred = np.asarray(y_pred)  # Garante consistência de tipos
    return np.abs(y_true - y_pred).mean()  # Calcula erro médio absoluto

# Lista de funções disponíveis para importação
__all__ = ['wape', 'smape', 'mae']
