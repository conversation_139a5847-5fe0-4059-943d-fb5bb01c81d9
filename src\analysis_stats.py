"""
Módulo para análise estatística de dados de vendas da Chilli Beans.
Fornece métricas de confiabilidade por loja e suporte para painéis operacionais.
"""
from __future__ import annotations
import pandas as pd
import numpy as np
from typing import Optional


def indice_confiabilidade_loja(df: pd.DataFrame) -> pd.DataFrame:
    """Calcula métricas de estabilidade por loja.

    Requer colunas: id_loja, data, valor
    Retorna DataFrame com: media, desvio, coef_var, iqr, taxa_dias_zero, n_dias
    """
    g = df.groupby('id_loja')
    stats = g['valor'].agg(['mean','std','median','count'])
    q1 = g['valor'].quantile(0.25)  # 25º percentil
    q3 = g['valor'].quantile(0.75)  # 75º percentil
    iqr = q3 - q1  # Intervalo interquartil
    zero_rate = g.apply(lambda x: (x['valor'] == 0).mean())  # Proporção de dias sem vendas
    
    out = pd.DataFrame({
        'media': stats['mean'],
        'desvio': stats['std'],
        'coef_var': stats['std'] / stats['mean'].replace(0,np.nan),  # Evita divisão por zero
        'iqr': iqr,
        'taxa_dias_zero': zero_rate,
        'n_dias': stats['count']
    }).reset_index()
    return out


def benchmark_uf_tipo(df: pd.DataFrame) -> Optional[pd.DataFrame]:
    needed = {'uf','Tipo_PDV','valor'}
    if not needed.issubset(df.columns):
        return None
        
    # Calcula estatísticas principais
    agg = (df.groupby(['uf','Tipo_PDV'])['valor']
           .agg(mediana='median', 
                p25=lambda s: s.quantile(0.25), 
                p75=lambda s: s.quantile(0.75))
           .reset_index())
    
    # Calcula média aparada - remove outliers
    def trimmed_mean(x):
        if len(x) < 5:  # Se poucos dados, usa média simples
            return x.mean()
        lo, hi = x.quantile(0.1), x.quantile(0.9)  # Remove 10% de cada extremo
        return x[(x>=lo)&(x<=hi)].mean()
        
    tm = df.groupby(['uf','Tipo_PDV'])['valor'].apply(trimmed_mean).reset_index(name='trimmed_mean')
    return agg.merge(tm, on=['uf','Tipo_PDV'])


def risco_cannibalizacao(df: pd.DataFrame) -> Optional[pd.DataFrame]:
    if not {'cidade','uf','id_loja'}.issubset(df.columns):
        return None
        
    # Conta número único de lojas por cidade
    densidade = (df[['id_loja','cidade','uf']].drop_duplicates()
                 .groupby(['uf','cidade']).size().reset_index(name='n_lojas'))
    
    # Flag para cidades com densidade acima da mediana
    mediana = densidade['n_lojas'].median()
    densidade['flag_alta_densidade'] = densidade['n_lojas'] > mediana
    return densidade

__all__ = ['indice_confiabilidade_loja','benchmark_uf_tipo','risco_cannibalizacao']
