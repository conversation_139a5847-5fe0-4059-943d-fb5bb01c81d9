# Relatórios Gerados

Lista dos principais artefatos exportados pelo notebook principal.

## Tabelas (`reports/tables/`)
- `qa_overview.csv`: métricas de qualidade dos dados (linhas, nulos, duplicatas, intervalo de datas, etc.).
- `baseline_metrics.csv`: WAPE, sMAPE, MAE por loja para cada baseline.
- `gbm_metrics.csv`: métricas agregadas do modelo LightGBM e, se aplicável, por segmento.
- `top_bottom_lojas.csv`: ranking de lojas (média diária e variância).
- `pareto_lojas.csv`: curva ABC de lojas.
- `pareto_produtos.csv`: curva ABC de produtos (se coluna disponível).
- `feature_importance.csv`: importância de features LightGBM.
- `shap_top_features.csv`: Top-10 features com interpretação.

## Plots (`reports/plots/`)
- `serie_diaria_total.png` / `.html`
- `serie_top6_lojas.png` / `.html`
- `boxplot_receita_tipo_pdv.png`
- `heatmap_uf_tipo_pdv.png` (ou ranking/treemap alternativos)
- `pareto_lojas.png`
- `pareto_produtos.png` (se aplicável)
- `baseline_error_distribution.png`
- `real_vs_pred_topN.png`
- `feature_importance.png`
- `shap_summary.png`

## Observações
- Nem todos os arquivos existirão se colunas opcionais estiverem ausentes (ex: Tipo_PDV, id_produto).
- Arquivos adicionais podem ser criados para análises exploratórias extras; manter convenção descritiva.

Atualize esta lista ao adicionar novos artefatos relevantes.
