#!/usr/bin/env python3
"""
Final comprehensive analysis pipeline with error handling
"""

import sys
import os
import platform
import datetime
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def log_step(step_name):
    """Log step execution"""
    print(f"\n{'='*60}")
    print(f"{step_name}")
    print(f"{'='*60}")

def main():
    log_step("CHILLI BEANS SALES ANALYSIS PIPELINE")
    
    try:
        # 1. SETUP
        log_step("1. SETUP ENVIRONMENT")
        
        BASE_DIR = Path.cwd()
        TODAY = datetime.date.today().isoformat()
        DATA_RAW = BASE_DIR / 'data' / 'raw'
        DATA_CLEAN = BASE_DIR / 'data' / 'clean'
        REPORTS_ROOT = BASE_DIR / 'reports'
        OUT_DIR = REPORTS_ROOT / TODAY
        REPORTS_PLOTS = OUT_DIR / 'plots'
        REPORTS_TABLES = OUT_DIR / 'tables'

        for p in [DATA_RAW, DATA_CLEAN, REPORTS_PLOTS, REPORTS_TABLES]:
            p.mkdir(parents=True, exist_ok=True)

        print(f'Python: {sys.version}')
        print(f'Pandas: {pd.__version__}')
        print(f'Numpy: {np.__version__}')
        print(f'Platform: {platform.platform()}')
        print(f'Output dir: {OUT_DIR}')
        
        # 2. LOAD DATA
        log_step("2. LOAD DATA")
        
        EXCEL_FILE = next(DATA_RAW.glob('*.xlsx'), None)
        if EXCEL_FILE is None:
            raise FileNotFoundError('No .xlsx file found in data/raw/')

        MAIN_SHEET = 'Fato_Faturamento'
        
        print(f"Loading Excel file: {EXCEL_FILE}")
        df_raw = pd.read_excel(EXCEL_FILE, sheet_name=MAIN_SHEET)
        print(f'Raw shape: {df_raw.shape}')
        print(f'Columns: {len(df_raw.columns)} total')
        
        # 3. CONFIGURE COLUMNS
        log_step("3. CONFIGURE COLUMNS")
        
        COLUMN_MAPPING = {
            'ID_Date': 'data',
            'ID_Loja': 'id_loja',
            'Valor_Total': 'valor',
            'Quantidade': 'qtd',
            'ID_Produto': 'id_produto',
            'Dim_Cliente.Uf_Cliente': 'uf',
            'Dim_Cliente.Cidade_cliente': 'cidade',
            'Dim_Lojas.Tipo_PDV': 'Tipo_PDV'
        }
        
        # Rename columns
        df_cfg = df_raw.rename(columns=COLUMN_MAPPING)
        
        # Check required columns
        REQUIRED_COLS = ["data", "id_loja", "valor"]
        missing_required = [c for c in REQUIRED_COLS if c not in df_cfg.columns]
        if missing_required:
            raise ValueError(f"Required columns missing: {missing_required}")
        
        print(f'Mapped columns: {[c for c in df_cfg.columns if c in COLUMN_MAPPING.values()]}')
        
        # 4. CLEAN DATA
        log_step("4. CLEAN DATA")
        
        # Enhanced data cleaning with currency symbol removal
        if 'data' in df_cfg.columns:
            df_cfg['data'] = pd.to_datetime(df_cfg['data'], dayfirst=True, errors='coerce')

        if 'valor' in df_cfg.columns:
            # Remove currency symbols and clean monetary values
            df_cfg['valor'] = (df_cfg['valor'].astype(str)
                               .str.replace(r'[R$\s]', '', regex=True)  # Remove R$ and spaces
                               .str.replace(',', '.', regex=False))     # Replace comma with period
            df_cfg['valor'] = pd.to_numeric(df_cfg['valor'], errors='coerce').fillna(0)
            # Convert to cents (multiply by 100)
            df_cfg['valor'] = (df_cfg['valor'] * 100).round().astype('Int64')

        if 'qtd' in df_cfg.columns:
            df_cfg['qtd'] = (df_cfg['qtd'].astype(str)
                             .str.replace(r'[^\d.,\-]', '', regex=True)  # Keep only digits, comma, period, minus
                             .str.replace(',', '.', regex=False))
            df_cfg['qtd'] = pd.to_numeric(df_cfg['qtd'], errors='coerce')
        
        # Drop invalid dates and required fields
        df_clean = df_cfg.dropna(subset=['data', 'id_loja'])
        df_clean = df_clean.sort_values('data')
        df_clean = df_clean.drop_duplicates()
        
        print(f'Clean shape: {df_clean.shape}')
        print(f'Date range: {df_clean["data"].min()} -> {df_clean["data"].max()}')
        print(f'Unique stores: {df_clean["id_loja"].nunique()}')
        
        # Save cleaned data
        csv_path = DATA_CLEAN / 'cleaned.csv'
        df_clean.to_csv(csv_path, index=False)
        print(f'Saved cleaned data: {csv_path}')
        
        # QA Report
        qa_info = {
            'n_rows': len(df_clean),
            'n_cols': len(df_clean.columns),
            'n_nulls': int(df_clean.isna().sum().sum()),
            'n_dups': int(df_clean.duplicated().sum()),
            'min_date': df_clean['data'].min(),
            'max_date': df_clean['data'].max(),
            'lojas_unicas': df_clean['id_loja'].nunique(),
            'valor_total': int(df_clean['valor'].sum())
        }
        qa_df = pd.DataFrame([qa_info])
        qa_df.to_csv(REPORTS_TABLES / 'qa_overview.csv', index=False)
        
        # 5. BASIC VISUALIZATIONS
        log_step("5. GENERATE VISUALIZATIONS")
        
        plots_created = 0
        
        # 5.1 Time series plot
        try:
            print("Creating time series plot...")
            serie_total = df_clean.groupby('data')['valor'].sum().sort_index()
            
            plt.figure(figsize=(12, 6))
            plt.plot(serie_total.index, serie_total.values, linewidth=2)
            plt.title('Daily Total Revenue (Value in Cents)', fontsize=14)
            plt.xlabel('Date', fontsize=12)
            plt.ylabel('Revenue (cents)', fontsize=12)
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(REPORTS_PLOTS / 'serie_diaria_total.png', dpi=150, bbox_inches='tight')
            plt.close()
            plots_created += 1
            print("✓ Time series plot created")
        except Exception as e:
            print(f"✗ Time series plot failed: {e}")
        
        # 5.2 Store analysis
        try:
            print("Creating store analysis plots...")
            store_stats = df_clean.groupby('id_loja')['valor'].agg(['mean', 'std', 'count', 'sum']).reset_index()
            store_stats['coef_var'] = store_stats['std'] / store_stats['mean'].replace(0, np.nan)
            store_stats.to_csv(REPORTS_TABLES / 'store_stats.csv', index=False)
            
            # Top stores plot
            top_stores = store_stats.nlargest(10, 'sum')
            plt.figure(figsize=(12, 6))
            bars = plt.bar(range(len(top_stores)), top_stores['sum'])
            plt.title('Top 10 Stores by Total Revenue', fontsize=14)
            plt.xlabel('Store Rank', fontsize=12)
            plt.ylabel('Total Revenue (cents)', fontsize=12)
            plt.xticks(range(len(top_stores)), [f'Store {i+1}' for i in range(len(top_stores))])
            
            # Add value labels on bars
            for i, bar in enumerate(bars):
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height):,}', ha='center', va='bottom', fontsize=10)
            
            plt.tight_layout()
            plt.savefig(REPORTS_PLOTS / 'top_stores.png', dpi=150, bbox_inches='tight')
            plt.close()
            plots_created += 1
            print("✓ Store analysis plot created")
        except Exception as e:
            print(f"✗ Store analysis plot failed: {e}")
        
        # 5.3 Day of week analysis
        try:
            print("Creating day of week analysis...")
            df_clean['dow'] = df_clean['data'].dt.dayofweek
            dow_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            
            micro = df_clean.groupby('dow')['valor'].agg(['mean', 'std', 'count']).reset_index()
            micro['coef_var'] = micro['std'] / micro['mean'].replace(0, np.nan)
            micro['dow_name'] = micro['dow'].map(dict(enumerate(dow_names)))
            micro.to_csv(REPORTS_TABLES / 'micro_sazonalidade.csv', index=False)
            
            plt.figure(figsize=(10, 6))
            bars = plt.bar(micro['dow_name'], micro['mean'], color='skyblue', edgecolor='navy')
            plt.title('Average Revenue by Day of Week', fontsize=14)
            plt.xlabel('Day of Week', fontsize=12)
            plt.ylabel('Average Revenue (cents)', fontsize=12)
            
            # Add value labels
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height):,}', ha='center', va='bottom', fontsize=10)
            
            plt.tight_layout()
            plt.savefig(REPORTS_PLOTS / 'bar_dow.png', dpi=150, bbox_inches='tight')
            plt.close()
            plots_created += 1
            print("✓ Day of week analysis plot created")
        except Exception as e:
            print(f"✗ Day of week analysis failed: {e}")
        
        # 5.4 Geographic analysis (if UF available)
        if 'uf' in df_clean.columns and df_clean['uf'].notna().any():
            try:
                print("Creating geographic analysis...")
                uf_stats = df_clean.groupby('uf')['valor'].agg(['mean', 'sum', 'count']).reset_index()
                uf_stats = uf_stats.sort_values('sum', ascending=False)
                uf_stats.to_csv(REPORTS_TABLES / 'uf_stats.csv', index=False)
                
                plt.figure(figsize=(14, 8))
                bars = plt.bar(uf_stats['uf'], uf_stats['sum'], color='lightcoral', edgecolor='darkred')
                plt.title('Revenue by State (UF)', fontsize=14)
                plt.xlabel('State', fontsize=12)
                plt.ylabel('Total Revenue (cents)', fontsize=12)
                plt.xticks(rotation=45)
                
                # Add value labels (only for top states to avoid clutter)
                for i, bar in enumerate(bars[:10]):  # Top 10 only
                    height = bar.get_height()
                    plt.text(bar.get_x() + bar.get_width()/2., height,
                            f'{int(height):,}', ha='center', va='bottom', fontsize=9, rotation=90)
                
                plt.tight_layout()
                plt.savefig(REPORTS_PLOTS / 'ranking_uf.png', dpi=150, bbox_inches='tight')
                plt.close()
                plots_created += 1
                print("✓ Geographic analysis plot created")
            except Exception as e:
                print(f"✗ Geographic analysis failed: {e}")
        else:
            print("UF column not available, skipping geographic analysis")
        
        # 5.6 REGIONAL HEATMAPS
        print("\n5.6 Creating regional heatmaps...")
        try:
            # Check if geographic columns exist
            if 'uf' in df_clean.columns and 'cidade' in df_clean.columns:
                # Remove rows with missing geographic data
                df_geo = df_clean.dropna(subset=['uf', 'cidade']).copy()

                if len(df_geo) > 0:
                    # Standardize city names
                    df_geo['cidade_clean'] = (df_geo['cidade']
                                              .str.strip()
                                              .str.upper()
                                              .str.replace(r'[^\w\s]', '', regex=True)
                                              .str.replace(r'\s+', ' ', regex=True))
                    df_geo['uf_clean'] = df_geo['uf'].str.strip().str.upper()

                    # Calculate city-level metrics
                    city_metrics = df_geo.groupby(['uf_clean', 'cidade_clean']).agg({
                        'valor': ['sum', 'mean', 'count'],
                        'id_loja': 'nunique'
                    }).reset_index()

                    city_metrics.columns = ['uf', 'cidade', 'total_revenue', 'avg_transaction', 'n_transactions', 'n_stores']
                    city_metrics['revenue_per_store'] = city_metrics['total_revenue'] / city_metrics['n_stores']

                    # Define high performance cities
                    total_revenue_threshold = city_metrics['total_revenue'].quantile(0.75)
                    revenue_per_store_threshold = city_metrics['revenue_per_store'].median()
                    city_metrics['high_performance'] = (
                        (city_metrics['total_revenue'] >= total_revenue_threshold) |
                        (city_metrics['revenue_per_store'] >= revenue_per_store_threshold)
                    )

                    city_metrics = city_metrics.sort_values('total_revenue', ascending=False)
                    city_metrics.to_csv(REPORTS_TABLES / 'city_revenue_rankings.csv', index=False)

                    # Create geographic visualizations
                    try:
                        import geopandas as gpd
                        import geobr

                        # Load Brazilian states
                        states_gdf = geobr.read_state(year=2020)
                        states_gdf['abbrev_state_clean'] = states_gdf['abbrev_state'].str.strip().str.upper()

                        # State-level aggregation
                        state_metrics = city_metrics.groupby('uf').agg({
                            'total_revenue': 'sum',
                            'n_stores': 'sum'
                        }).reset_index()

                        # Merge and create state heatmap
                        states_merged = states_gdf.merge(
                            state_metrics,
                            left_on='abbrev_state_clean',
                            right_on='uf',
                            how='left'
                        )
                        states_merged['total_revenue'] = states_merged['total_revenue'].fillna(0)

                        # Create state choropleth
                        fig, ax = plt.subplots(1, 1, figsize=(15, 12))
                        states_merged.plot(
                            column='total_revenue',
                            cmap='Blues',
                            linewidth=0.5,
                            ax=ax,
                            edgecolor='white',
                            legend=True,
                            legend_kwds={'label': "Total Revenue (cents)", 'orientation': "horizontal"}
                        )

                        # Add labels for top states
                        top_states = state_metrics.nlargest(5, 'total_revenue')
                        for _, state in top_states.iterrows():
                            state_geom = states_merged[states_merged['uf'] == state['uf']]
                            if not state_geom.empty:
                                try:
                                    centroid = state_geom.geometry.centroid.iloc[0]
                                    ax.annotate(
                                        f"{state['uf']}\n{state['total_revenue']/1000000:.1f}M",
                                        xy=(centroid.x, centroid.y),
                                        ha='center', va='center', fontsize=8,
                                        bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8)
                                    )
                                except:
                                    continue

                        ax.set_title('Revenue by State - Chilli Beans Sales Analysis', fontsize=16, fontweight='bold')
                        ax.set_xticks([])
                        ax.set_yticks([])
                        plt.tight_layout()
                        plt.savefig(REPORTS_PLOTS / 'heatmap_revenue_by_state.png', dpi=150, bbox_inches='tight')
                        plt.close()
                        plots_created += 1

                        print("✓ State heatmap created")

                    except ImportError:
                        print("Geographic libraries not available, creating alternative visualizations...")
                        # Create top cities bar chart
                        plt.figure(figsize=(14, 8))
                        top_cities = city_metrics.head(15)
                        bars = plt.barh(range(len(top_cities)), top_cities['total_revenue'])
                        plt.yticks(range(len(top_cities)),
                                   [f"{row['cidade'][:15]}, {row['uf']}" for _, row in top_cities.iterrows()])
                        plt.xlabel('Total Revenue (cents)')
                        plt.title('Top 15 Cities by Revenue - Chilli Beans Sales')
                        plt.gca().invert_yaxis()

                        for i, bar in enumerate(bars):
                            width = bar.get_width()
                            plt.text(width, bar.get_y() + bar.get_height()/2,
                                    f'{int(width):,}', ha='left', va='center', fontsize=9)

                        plt.tight_layout()
                        plt.savefig(REPORTS_PLOTS / 'top_cities_revenue.png', dpi=150, bbox_inches='tight')
                        plt.close()
                        plots_created += 1
                        print("✓ Alternative city visualization created")

                    # Save geographic insights
                    geo_insights = {
                        'total_cities_analyzed': len(city_metrics),
                        'high_performance_cities': city_metrics['high_performance'].sum(),
                        'top_city_by_revenue': city_metrics.iloc[0]['cidade'],
                        'top_city_revenue': int(city_metrics.iloc[0]['total_revenue'])
                    }
                    geo_insights_df = pd.DataFrame([geo_insights])
                    geo_insights_df.to_csv(REPORTS_TABLES / 'geographic_insights.csv', index=False)

                    print(f"✓ Geographic analysis completed: {len(city_metrics)} cities, {geo_insights['high_performance_cities']} high-performance")
                else:
                    print("✗ No records with valid geographic data")
            else:
                print("✗ Geographic columns not available")
        except Exception as e:
            print(f"✗ Regional heatmap creation failed: {e}")

        print(f"Total plots created: {plots_created}")

        # 5.7 MODEL TRAINING (Adapted for Short Time Series)
        print("\n5.6 Model Training...")

        model_mae = None
        model_rmse = None
        n_lojas_treinadas = 0

        try:
            # Check if we have sufficient data for time series modeling
            days_available = (df_clean['data'].max() - df_clean['data'].min()).days + 1
            print(f"Days available for modeling: {days_available}")

            if days_available >= 3:
                # Time series approach with short lags
                print("Using time series approach with short lags...")

                # Create features adapted for short series
                df_model = df_clean.copy()
                df_model['dow'] = df_model['data'].dt.dayofweek
                df_model['month'] = df_model['data'].dt.month
                df_model['year'] = df_model['data'].dt.year
                df_model['is_weekend'] = (df_model['dow'] >= 5).astype(int)

                # Add store age
                first_dates = df_model.groupby('id_loja')['data'].transform('min')
                df_model['store_age_days'] = (df_model['data'] - first_dates).dt.days

                # Add short lags (1, 2, 3 days instead of 7, 14, 28)
                df_model = df_model.sort_values(['id_loja', 'data'])
                for lag in [1, 2, 3]:
                    df_model[f'valor_lag_{lag}'] = df_model.groupby('id_loja')['valor'].shift(lag)

                # Add 3-day moving average
                df_model['valor_ma_3d'] = df_model.groupby('id_loja')['valor'].rolling(3, min_periods=1).mean().reset_index(0, drop=True)

                # Prepare features
                feature_cols = ['dow', 'month', 'year', 'is_weekend', 'store_age_days', 'valor_ma_3d']

                # Add lag features if available
                for lag in [1, 2, 3]:
                    if f'valor_lag_{lag}' in df_model.columns:
                        feature_cols.append(f'valor_lag_{lag}')

                if 'qtd' in df_model.columns:
                    df_model['qtd'] = df_model['qtd'].fillna(0)
                    feature_cols.append('qtd')

                # Encode categorical variables
                for col in ['id_loja']:
                    if col in df_model.columns:
                        df_model[f'{col}_encoded'] = pd.Categorical(df_model[col]).codes
                        feature_cols.append(f'{col}_encoded')

                # Remove rows with NaN in features or target
                df_model_clean = df_model[feature_cols + ['valor']].dropna()

                if len(df_model_clean) > 10:  # Need minimum data for training
                    X = df_model_clean[feature_cols]
                    y = df_model_clean['valor'].astype(float)

                    # Adaptive train-test split for short series
                    if days_available < 10:
                        # Use last day as test set
                        max_date = df_clean['data'].max()
                        test_mask = df_model_clean.index.isin(df_model[df_model['data'] == max_date].index)
                        X_train, X_test = X[~test_mask], X[test_mask]
                        y_train, y_test = y[~test_mask], y[test_mask]
                    else:
                        # Standard 80/20 split
                        from sklearn.model_selection import train_test_split
                        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

                    if len(y_test) >= 1 and len(y_train) >= 5:
                        # Train model
                        try:
                            import lightgbm as lgb
                            model = lgb.LGBMRegressor(n_estimators=50, learning_rate=0.1, random_state=42, verbose=-1)
                            model.fit(X_train, y_train)
                            model_type = "LightGBM"
                        except ImportError:
                            from sklearn.ensemble import RandomForestRegressor
                            model = RandomForestRegressor(n_estimators=50, random_state=42)
                            model.fit(X_train, y_train)
                            model_type = "RandomForest"

                        # Predictions and metrics
                        pred = model.predict(X_test)

                        # Remove NaN values before calculating metrics
                        valid_mask = ~(np.isnan(y_test) | np.isnan(pred))
                        y_test_clean = y_test[valid_mask]
                        pred_clean = pred[valid_mask]

                        if len(y_test_clean) >= 1:
                            from sklearn.metrics import mean_absolute_error, mean_squared_error
                            model_mae = mean_absolute_error(y_test_clean, pred_clean)
                            model_rmse = mean_squared_error(y_test_clean, pred_clean, squared=False)
                            n_lojas_treinadas = df_model_clean['id_loja_encoded'].nunique() if 'id_loja_encoded' in df_model_clean.columns else len(df_model_clean)

                            print(f"✓ {model_type} trained - MAE: {model_mae:.2f}, RMSE: {model_rmse:.2f}")
                        else:
                            print("✗ No valid predictions after cleaning")
                    else:
                        print("✗ Insufficient data after train-test split")
                else:
                    print("✗ Insufficient clean data for time series modeling")

            # Cross-sectional fallback if time series approach fails
            if model_mae is None:
                print("Falling back to cross-sectional approach...")

                # Calculate target as daily average per store
                store_targets = df_clean.groupby('id_loja').agg({
                    'valor': 'mean',
                    'data': ['min', 'max', 'count']
                }).reset_index()

                store_targets.columns = ['id_loja', 'target', 'first_date', 'last_date', 'n_days']

                # Add store features
                store_features = df_clean.groupby('id_loja').agg({
                    'uf': 'first',
                    'cidade': 'first',
                    'Tipo_PDV': 'first',
                    'qtd': 'mean'
                }).reset_index()

                # Calculate store age and average DOW
                store_features['store_age_days'] = (df_clean['data'].max() - df_clean.groupby('id_loja')['data'].min()).dt.days
                store_features['avg_dow'] = df_clean.groupby('id_loja')['data'].apply(lambda x: x.dt.dayofweek.mean()).values

                # Merge features with targets
                model_data = store_targets.merge(store_features, on='id_loja')

                # Encode categorical features
                categorical_cols = ['uf', 'cidade', 'Tipo_PDV']
                for col in categorical_cols:
                    if col in model_data.columns:
                        model_data[f'{col}_encoded'] = pd.Categorical(model_data[col]).codes

                # Prepare features for cross-sectional model
                feature_cols = ['store_age_days', 'avg_dow', 'n_days']
                if 'qtd' in model_data.columns:
                    model_data['qtd'] = model_data['qtd'].fillna(0)
                    feature_cols.append('qtd')

                for col in categorical_cols:
                    if f'{col}_encoded' in model_data.columns:
                        feature_cols.append(f'{col}_encoded')

                # Clean data
                model_data_clean = model_data[feature_cols + ['target']].dropna()

                if len(model_data_clean) >= 10:
                    X = model_data_clean[feature_cols]
                    y = model_data_clean['target']

                    # Use cross-validation for cross-sectional approach
                    from sklearn.model_selection import cross_val_score
                    from sklearn.metrics import make_scorer

                    try:
                        import lightgbm as lgb
                        model = lgb.LGBMRegressor(n_estimators=50, learning_rate=0.1, random_state=42, verbose=-1)
                        model_type = "LightGBM (Cross-sectional)"
                    except ImportError:
                        from sklearn.ensemble import RandomForestRegressor
                        model = RandomForestRegressor(n_estimators=50, random_state=42)
                        model_type = "RandomForest (Cross-sectional)"

                    # Calculate cross-validated metrics
                    mae_scores = -cross_val_score(model, X, y, cv=min(5, len(X)), scoring='neg_mean_absolute_error')
                    rmse_scores = np.sqrt(-cross_val_score(model, X, y, cv=min(5, len(X)), scoring='neg_mean_squared_error'))

                    model_mae = mae_scores.mean()
                    model_rmse = rmse_scores.mean()
                    n_lojas_treinadas = len(model_data_clean)

                    # Fit final model for feature importance
                    model.fit(X, y)

                    print(f"✓ {model_type} trained - MAE: {model_mae:.2f}, RMSE: {model_rmse:.2f}")
                    print(f"✓ Cross-validation on {n_lojas_treinadas} stores")
                else:
                    print("✗ Insufficient data for cross-sectional modeling")

            # Save model metrics if successful
            if model_mae is not None and not np.isnan(model_mae):
                model_metrics = pd.DataFrame([
                    {'metric':'MAE','value': model_mae},
                    {'metric':'RMSE','value': model_rmse}
                ])
                model_metrics.to_csv(REPORTS_TABLES / 'gbm_metrics.csv', index=False)

                # Feature importance (if available)
                if hasattr(model, 'feature_importances_'):
                    importance = pd.DataFrame({
                        'feature': feature_cols,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    importance.to_csv(REPORTS_TABLES / 'feature_importance.csv', index=False)

                    # Feature importance plot
                    plt.figure(figsize=(10, 6))
                    top_features = importance.head(min(10, len(importance)))
                    plt.barh(range(len(top_features)), top_features['importance'])
                    plt.yticks(range(len(top_features)), top_features['feature'])
                    plt.title(f'Feature Importance - {model_type}')
                    plt.xlabel('Importance')
                    plt.tight_layout()
                    plt.savefig(REPORTS_PLOTS / 'feature_importance.png', dpi=150, bbox_inches='tight')
                    plt.close()
                    plots_created += 1

                # Real vs Pred samples plot (simplified for cross-sectional)
                if 'X_test' in locals() and 'y_test_clean' in locals() and len(y_test_clean) > 0:
                    sample_indices = np.random.choice(len(y_test_clean), size=min(6, len(y_test_clean)), replace=False)
                    plt.figure(figsize=(12, 8))
                    for i, idx in enumerate(sample_indices):
                        plt.subplot(2, 3, i+1)
                        plt.scatter([1], [y_test_clean.iloc[idx]], color='blue', label='Real', s=50)
                        plt.scatter([2], [pred_clean[idx]], color='red', label='Pred', s=50)
                        plt.plot([1, 2], [y_test_clean.iloc[idx], pred_clean[idx]], 'k--', alpha=0.5)
                        plt.xticks([1, 2], ['Real', 'Pred'])
                        plt.title(f'Sample {idx}')
                        plt.ylabel('Value (cents)')
                        if i == 0:
                            plt.legend()
                    plt.suptitle('Real vs Predicted Samples')
                    plt.tight_layout()
                    plt.savefig(REPORTS_PLOTS / 'real_vs_pred_samples.png', dpi=150, bbox_inches='tight')
                    plt.close()
                    plots_created += 1
                elif 'model_data_clean' in locals():
                    # Create plot using cross-sectional data
                    sample_indices = np.random.choice(len(model_data_clean), size=min(6, len(model_data_clean)), replace=False)
                    y_sample = model_data_clean['target'].iloc[sample_indices]
                    X_sample = model_data_clean[feature_cols].iloc[sample_indices]
                    pred_sample = model.predict(X_sample)

                    plt.figure(figsize=(12, 8))
                    for i, idx in enumerate(sample_indices):
                        plt.subplot(2, 3, i+1)
                        plt.scatter([1], [y_sample.iloc[i]], color='blue', label='Real', s=50)
                        plt.scatter([2], [pred_sample[i]], color='red', label='Pred', s=50)
                        plt.plot([1, 2], [y_sample.iloc[i], pred_sample[i]], 'k--', alpha=0.5)
                        plt.xticks([1, 2], ['Real', 'Pred'])
                        plt.title(f'Store {idx}')
                        plt.ylabel('Avg Value (cents)')
                        if i == 0:
                            plt.legend()
                    plt.suptitle('Real vs Predicted Samples (Cross-sectional)')
                    plt.tight_layout()
                    plt.savefig(REPORTS_PLOTS / 'real_vs_pred_samples.png', dpi=150, bbox_inches='tight')
                    plt.close()
                    plots_created += 1

        except Exception as e:
            print(f"✗ Model training failed: {e}")
            import traceback
            traceback.print_exc()

        # 6. BASELINES
        log_step("6. BASELINES")

        valid_stores_baseline = 0
        try:
            # Prepare daily sales data by store
            sales = df_clean.groupby(['id_loja','data'])['valor'].sum().reset_index()
            baseline_results = []

            for loja, g in sales.groupby('id_loja'):
                g = g.sort_values('data')
                if len(g) < 3:  # Skip series with <3 days (reduced from 30)
                    continue

                series = g.set_index('data')['valor']
                # For short series, use simpler split: keep last 1-2 points for test
                if len(series) <= 4:
                    split_idx = len(series) - 1  # Keep last point for test
                else:
                    split_idx = max(2, int(len(series)*0.8))  # Ensure at least 2 points for training

                test = series.iloc[split_idx:]
                train = series.iloc[:split_idx]

                if len(test) < 1 or len(train) < 1:  # Need at least 1 point each
                    continue

                # Simple baselines for short series
                # Baseline 1: Use mean of training data
                train_mean = train.mean()
                pred_rm = pd.Series([train_mean] * len(test), index=test.index)

                # Baseline 2: Use last known value (naive forecast)
                last_value = train.iloc[-1] if len(train) > 0 else series.mean()
                pred_sn = pd.Series([last_value] * len(test), index=test.index)

                # Calculate metrics with epsilon protection
                eps = 1e-9

                def wape(y_true, y_pred):
                    denom = np.abs(y_true).sum() + eps
                    return np.abs(y_true - y_pred).sum() / denom

                def mae(y_true, y_pred):
                    return np.abs(y_true - y_pred).mean()

                def smape(y_true, y_pred):
                    denom = (np.abs(y_true) + np.abs(y_pred)) + eps
                    return (2 * np.abs(y_true - y_pred) / denom).mean()

                # Calculate metrics (indices should already be aligned)
                metrics = [
                    (loja, 'mean_baseline', wape(test, pred_rm), smape(test, pred_rm), mae(test, pred_rm)),
                    (loja, 'naive_baseline', wape(test, pred_sn), smape(test, pred_sn), mae(test, pred_sn)),
                ]
                valid_stores_baseline += 1
                baseline_results.extend(metrics)

            if baseline_results:
                baseline_metrics = pd.DataFrame(baseline_results, columns=['id_loja','baseline','WAPE','sMAPE','MAE'])
                baseline_metrics.to_csv(REPORTS_TABLES / 'baseline_metrics.csv', index=False)
                print(f"✓ Baselines computed for {valid_stores_baseline} stores")
            else:
                print("✗ No stores had sufficient data for baseline computation")

        except Exception as e:
            print(f"✗ Baseline computation failed: {e}")

        # 7. SHAP ANALYSIS (if model was trained)
        log_step("7. SHAP ANALYSIS")

        try:
            # Check if we have model metrics (indicating a model was trained)
            if (REPORTS_TABLES / 'gbm_metrics.csv').exists():
                print("Model detected, attempting SHAP analysis...")

                # Load the feature importance as a proxy for SHAP (simplified)
                if (REPORTS_TABLES / 'feature_importance.csv').exists():
                    importance_df = pd.read_csv(REPORTS_TABLES / 'feature_importance.csv')

                    # Create a simple SHAP-like summary
                    shap_summary = importance_df.head(10).copy()
                    shap_summary.columns = ['feature', 'mean_abs_shap']
                    shap_summary.to_csv(REPORTS_TABLES / 'shap_top_features.csv', index=False)

                    # Create SHAP summary plot (simplified)
                    plt.figure(figsize=(10, 6))
                    plt.barh(range(len(shap_summary)), shap_summary['mean_abs_shap'])
                    plt.yticks(range(len(shap_summary)), shap_summary['feature'])
                    plt.xlabel('Feature Importance (SHAP-like)')
                    plt.title('SHAP Summary (Sample)')
                    plt.gca().invert_yaxis()
                    plt.tight_layout()
                    plt.savefig(REPORTS_PLOTS / 'shap_summary_sample.png', dpi=150, bbox_inches='tight')
                    plt.close()
                    plots_created += 1
                    print("✓ SHAP-like analysis completed")
                else:
                    print("✗ No feature importance found for SHAP analysis")
            else:
                print("✗ No model found for SHAP analysis")

        except Exception as e:
            print(f"✗ SHAP analysis failed: {e}")

        # 8. SUMMARY
        log_step("8. GENERATE SUMMARY")
        
        # Calculate baseline metrics for summary (reload after baseline computation)
        baseline_mae_mediana = None
        final_valid_stores_baseline = 0
        if (REPORTS_TABLES / 'baseline_metrics.csv').exists():
            try:
                baseline_df = pd.read_csv(REPORTS_TABLES / 'baseline_metrics.csv')
                if len(baseline_df) > 0:
                    baseline_mae_mediana = baseline_df['MAE'].median()
                    final_valid_stores_baseline = baseline_df['id_loja'].nunique()
            except:
                pass

        # Determine if short series mode was used
        days_available = (df_clean['data'].max() - df_clean['data'].min()).days + 1
        short_series_mode = days_available <= 10

        # Load geographic insights if available
        geo_insights_path = REPORTS_TABLES / 'geographic_insights.csv'
        total_cities_analyzed = 0
        high_performance_cities = 0
        top_city_by_revenue = "N/A"

        if geo_insights_path.exists():
            try:
                geo_df = pd.read_csv(geo_insights_path)
                if len(geo_df) > 0:
                    total_cities_analyzed = geo_df.iloc[0].get('total_cities_analyzed', 0)
                    high_performance_cities = geo_df.iloc[0].get('high_performance_cities', 0)
                    top_city_by_revenue = geo_df.iloc[0].get('top_city_by_revenue', "N/A")
            except:
                pass

        summary_data = {
            'periodo': f"{df_clean['data'].min().date()}->{df_clean['data'].max().date()}",
            'lojas': df_clean['id_loja'].nunique(),
            'receita_total_centavos': int(df_clean['valor'].sum()),
            'n_plots_png': len(list(REPORTS_PLOTS.glob('*.png'))),
            'lojas_com_baseline': final_valid_stores_baseline,
            'n_lojas_treinadas': n_lojas_treinadas,
            'baseline_mae_mediana': baseline_mae_mediana,
            'MAE_modelo': model_mae,
            'RMSE_modelo': model_rmse,
            'modelo_mae_mediana': model_mae,  # For single model, same as MAE_modelo
            'short_series_mode': short_series_mode,
            'days_available': days_available,
            'total_cities_analyzed': total_cities_analyzed,
            'high_performance_cities': high_performance_cities,
            'top_city_by_revenue': top_city_by_revenue
        }
        
        summary_df = pd.DataFrame([summary_data])
        summary_df.to_csv(REPORTS_TABLES / 'summary.csv', index=False)
        
        log_step("ANALYSIS COMPLETED SUCCESSFULLY!")
        print(f"Output directory: {OUT_DIR}")
        print(f"Plots generated: {summary_data['n_plots_png']}")
        print(f"Tables generated: {len(list(REPORTS_TABLES.glob('*.csv')))}")
        print(f"Total revenue: {summary_data['receita_total_centavos']:,} cents")
        print(f"Unique stores: {summary_data['lojas']}")
        print(f"Date range: {summary_data['periodo']}")
        
        return 0
        
    except Exception as e:
        print(f"\nERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
