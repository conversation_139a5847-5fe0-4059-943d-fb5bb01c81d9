{"cells": [{"cell_type": "markdown", "id": "7441d1f4", "metadata": {}, "source": ["# Notebook Principal: <PERSON><PERSON><PERSON>\n", "\n", "Este notebook executa todo o pipeline: Setup, I/O, Config, Limpeza, EDA (inclui ICL, Outliers, Micro-sazonalidade, Choropleth), Baselines, Modelo, Explainability, Export & Sumário.\n", "\n", "> Execução limpa: Reiniciar kernel e usar Run All. Não executar células isoladas fora de ordem.\n", "\n", "## <PERSON><PERSON><PERSON><PERSON>\n", "1. <PERSON><PERSON>\n", "2. I/O & Leitura\n", "3. <PERSON>fig\n", "4. Limpeza & Padronização\n", "5. EDA\n", "   - 5.1 ICL (Índice de Confiabilidade por Loja)\n", "   - 5.2 Outliers Operacionais\n", "   - 5.3 Micro-sazonalidade (DOW)\n", "   - 5.4 Choropleth Receita por UF / Ranking\n", "6. <PERSON><PERSON> (+ 6.1 Forecast Curtíssimo Prazo)\n", "7. <PERSON><PERSON> (LightGBM)\n", "8. Explainability (SHAP)\n", "9. Export & Sumário\n", "\n", "---\n", "Checklist r<PERSON><PERSON><PERSON> antes de rodar:\n", "- Excel em `data/raw/`?\n", "- No<PERSON> da aba a<PERSON>stado (`MAIN_SHEET`)?\n", "- `COLUMN_MAPPING` coerente com cabeçalhos reais?\n", "- Dependências instaladas (ver `requirements.txt`)?\n", "---"]}, {"cell_type": "markdown", "id": "65488f9a", "metadata": {}, "source": ["## 1. <PERSON>up\n", "\n", "Comentário inicial (Objetivo | Entradas | Saídas | Premissas | Possíveis erros)\n", "\n", "**Objetivo:** Configurar ambiente, imports e caminhos.\n", "**Entradas:** Pacotes instalados no ambiente, arquivos em `data/raw`.\n", "**Saídas:** Variáveis globais de caminho; confirmação de versões.\n", "**Premissas:** Dependências instaladas conforme README.\n", "**Possíveis erros:** <PERSON><PERSON><PERSON><PERSON> não encontrado (dep não instalada), caminho inexistente.\n"]}, {"cell_type": "code", "execution_count": null, "id": "e46fc89a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python 3.12.7 | packaged by conda-forge | (main, Oct  4 2024, 15:47:54) [MSC v.1941 64 bit (AMD64)]\n", "Pandas 2.2.3\n", "Numpy 2.1.3\n", "Platform Windows-11-10.0.26100-SP0\n"]}], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Importar bibliotecas, definir caminhos base e checar versões.\n", "# Entradas: Pacotes instalados; estrutura de pastas criada.\n", "# Saídas: Variáveis PATHS, print de versões principais.\n", "# Premissas: Diretórios data/raw, data/clean, reports/ já existem.\n", "# Possíveis erros: ImportError se dependências ausentes.\n", "\n", "import sys, os, platform, datetime\n", "from pathlib import Path\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import plotly.express as px\n", "\n", "# Detecta raiz do projeto assumindo que notebook está em notebooks/\n", "BASE_DIR = Path.cwd().parent if (Path.cwd().name == 'notebooks') else Path.cwd()\n", "TODAY = datetime.date.today().isoformat()\n", "DATA_RAW = BASE_DIR / 'data' / 'raw'\n", "DATA_CLEAN = BASE_DIR / 'data' / 'clean'\n", "REPORTS_ROOT = BASE_DIR / 'reports'\n", "OUT_DIR = REPORTS_ROOT / TODAY  # versionamento por data\n", "REPORTS_PLOTS = OUT_DIR / 'plots'\n", "REPORTS_TABLES = OUT_DIR / 'tables'\n", "\n", "for p in [DATA_RAW, DATA_CLEAN, REPORTS_PLOTS, REPORTS_TABLES]:\n", "    p.mkdir(parents=True, exist_ok=True)\n", "\n", "print('Python', sys.version)\n", "print('Pandas', pd.__version__)\n", "print('Numpy', np.__version__)\n", "print('Platform', platform.platform())\n", "print('Output dir:', OUT_DIR)"]}, {"cell_type": "markdown", "id": "3106d9b5", "metadata": {}, "source": ["## 2. I/O & Leitura\n", "\n", "**Objetivo:** Carregar planilha Excel original e inspecionar colunas.\n", "**Entradas:** Arquivo Excel em `data/raw`.\n", "**Saídas:** DataFrame bruto e lista de colunas.\n", "**Premissas:** Existe um arquivo .xlsx válido.\n", "**Possíveis erros:** Arquivo não encontrado, aba inexistente, encoding/formato inválido."]}, {"cell_type": "code", "execution_count": 6, "id": "abf97c6b", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "Nenhum arquivo .xlsx encontrado em data/raw/", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 13\u001b[39m\n\u001b[32m     11\u001b[39m EXCEL_FILE = \u001b[38;5;28mnext\u001b[39m(DATA_RAW.glob(\u001b[33m'\u001b[39m\u001b[33m*.xlsx\u001b[39m\u001b[33m'\u001b[39m), \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m)\n\u001b[32m     12\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m EXCEL_FILE \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m13\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m(\u001b[33m'\u001b[39m\u001b[33mNenhum arquivo .xlsx encontrado em data/raw/\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m     15\u001b[39m \u001b[38;5;66;03m# Definir nome da aba principal (ajustar se necessário)\u001b[39;00m\n\u001b[32m     16\u001b[39m MAIN_SHEET = \u001b[33m'\u001b[39m\u001b[33mPlanilha1\u001b[39m\u001b[33m'\u001b[39m\n", "\u001b[31mFileNotFoundError\u001b[39m: Nenhum arquivo .xlsx encontrado em data/raw/"]}], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Carregar Excel e exibir colunas.\n", "# Entradas: Ar<PERSON>vo Excel principal.\n", "# Saídas: df_raw.head(), lista de colu<PERSON>.\n", "# Premissas: Nome do arquivo ajustado abaixo.\n", "# Possíveis erros: FileNot<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ValueError em leitura.\n", "\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "EXCEL_FILE = next(DATA_RAW.glob('*.xlsx'), None)\n", "if EXCEL_FILE is None:\n", "    raise FileNotFoundError('Nenhum arquivo .xlsx encontrado em data/raw/')\n", "\n", "# Definir nome da aba principal (ajustar se necessário)\n", "MAIN_SHEET = 'Fato_Faturamento'\n", "\n", "xls = pd.ExcelFile(EXCEL_FILE)\n", "print('<PERSON><PERSON> disponíveis:', xls.sheet_names)\n", "if MAIN_SHEET not in xls.sheet_names:\n", "    raise ValueError(f'Aba {MAIN_SHEET} não encontrada. Ajuste MAIN_SHEET.')\n", "\n", "df_raw = pd.read_excel(EXCEL_FILE, sheet_name=MAIN_SHEET)\n", "print('<PERSON><PERSON>pe bruto:', df_raw.shape)\n", "print('Colunas:', list(df_raw.columns))\n", "df_raw.head()"]}, {"cell_type": "markdown", "id": "ed3375ca", "metadata": {}, "source": ["## 3. Config\n", "\n", "**Objetivo:** Definir mapeamento de colunas origem→canônicas e validar presença de obrigatórias.\n", "**Entradas:** `df_raw` e dict de mapeamento.\n", "**<PERSON><PERSON><PERSON>:** `df_cfg` com colunas renomeadas.\n", "**Premissas:** Colunas originais existem na planilha.\n", "**Possíveis erros:** Falta de coluna obrigatória após mapeamento."]}, {"cell_type": "code", "execution_count": null, "id": "2c8fc914", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Mapear colunas para canônicas e validar.\n", "# Entradas: df_raw, dict mapping.\n", "# Saídas: df_cfg.\n", "# Premissas: Colunas originais listadas existem.\n", "# Possíveis erros: ValueError se faltar coluna obrigatória.\n", "\n", "from src.io_utils import map_and_validate, REQUIRED_COLS\n", "\n", "# Ajustar mapeamento conforme nomes reais do Excel.\n", "COLUMN_MAPPING = {\n", "    'ID_Date': 'data',\n", "    'ID_Loja': 'id_loja',\n", "    'Valor_Total': 'valor',\n", "    'Quantidade': 'qtd',\n", "    'ID_Produto': 'id_produto',\n", "    'Dim_Cliente.Uf_Cliente': 'uf',\n", "    'Dim_Cliente.Cidade_cliente': 'cidade',\n", "    'Dim_Lojas.Tipo_PDV': 'Tipo_PDV'\n", "}\n", "\n", "missing_source = [c for c in COLUMN_MAPPING if c not in df_raw.columns]\n", "print('Colunas origem ausentes (ignorar se realmente não existem):', missing_source)\n", "\n", "try:\n", "    df_cfg = map_and_validate(df_raw, COLUMN_MAPPING)\n", "    print('Colunas a<PERSON>ós mapping:', df_cfg.columns.tolist())\n", "except ValueError as e:\n", "    raise\n", "\n", "df_cfg.head()"]}, {"cell_type": "markdown", "id": "a1841ace", "metadata": {}, "source": ["## 4. Limpeza & Padronização\n", "\n", "**Objetivo:** Coerção de tipos, normalização monetária, remoção de nulos críticos e duplicatas.\n", "**Entradas:** `df_cfg`.\n", "**Sa<PERSON><PERSON>:** `df_clean` salvo em CSV/Parquet + QA report.\n", "**Premissas:** Mapeamento executado.\n", "**Possíveis erros:** Falhas na escrita de parquet (dependência)."]}, {"cell_type": "code", "execution_count": null, "id": "5fe864a8", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Limpar e salvar datasets + QA.\n", "# Entradas: df_cfg.\n", "# Saídas: df_clean, arquivos cleaned.csv / cleaned.parquet e qa_overview.csv.\n", "# Premissas: Pastas de saída existem.\n", "# Possíveis erros: <PERSON><PERSON>ha ao salvar parquet.\n", "\n", "from src.io_utils import coerce_types, basic_clean, save_outputs\n", "\n", "# Pré-limpeza específica antes do coerce: datas dayfirst e valor em centavos robusto\n", "if 'data' in df_cfg.columns:\n", "    df_cfg['data'] = pd.to_datetime(df_cfg['data'], dayfirst=True, errors='coerce')\n", "if 'valor' in df_cfg.columns:\n", "    # Remove símbolos monetários e transforma vírgula decimal\n", "    df_cfg['valor'] = (df_cfg['valor'].astype(str)\n", "                       .str.replace(r'[^0-9,.-]', '', regex=True)\n", "                       .str.replace(',', '.', regex=False))\n", "\n", "if 'qtd' in df_cfg.columns:\n", "    df_cfg['qtd'] = (df_cfg['qtd'].astype(str)\n", "                     .str.replace(r'[^0-9,.-]', '', regex=True)\n", "                     .str.replace(',', '.', regex=False))\n", "\n", "# A<PERSON>a coerção genérica (inclui multiplicar valor por 100)\n", "df_clean = coerce_types(df_cfg.copy())\n", "# Dropar datas inválidas após coerce\n", "df_clean = df_clean.dropna(subset=['data'])\n", "\n", "# Limpeza básica\n", "df_clean = basic_clean(df_clean)\n", "\n", "csv_path, pq_path = save_outputs(df_clean, DATA_CLEAN, REPORTS_TABLES)\n", "print('Salvo CSV:', csv_path)\n", "print('<PERSON><PERSON> (se criado):', pq_path)\n", "print('Intervalo de datas:', df_clean['data'].min(), '→', df_clean['data'].max())\n", "print('Lojas únicas:', df_clean['id_loja'].nunique())\n", "\n", "df_clean.head()"]}, {"cell_type": "markdown", "id": "2993d3da", "metadata": {}, "source": ["## 5. EDA\n", "\n", "**Objetivo:** Explorar padrões temporais, lojas com maior contribuição e variação geográfica / Tipo_PDV.\n", "**Entradas:** `df_clean`.\n", "**Saídas:** Gráficos salvos em `reports/plots/` e tabelas auxiliares em `reports/tables/`.\n", "**Premissas:** Colunas opcionais podem faltar (Tipo_PDV, id_produto).\n", "**Possíveis erros:** DataFrame vazio, falta de colunas opcionais."]}, {"cell_type": "markdown", "id": "9febf279", "metadata": {}, "source": ["### 5.4 <PERSON><PERSON>let<PERSON> Receita Média por UF"]}, {"cell_type": "code", "execution_count": null, "id": "5f9e54bb", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: <PERSON><PERSON><PERSON><PERSON> choropleth de receita média por UF (fallback ranking se shape falhar).\n", "# Entradas: df_clean (coluna uf).\n", "# Saídas: choropleth_uf.html/png ou ranking_uf.png.\n", "# Premissas: Col<PERSON> uf preenchida.\n", "# Possíveis erros: <PERSON><PERSON><PERSON>, ausência de uf.\n", "\n", "import pandas as pd\n", "import plotly.express as px\n", "from src.geo_utils import load_states_geojson, infer_featureidkey, UF_TO_CODE_IBGE\n", "\n", "if 'uf' in df_clean.columns:\n", "    agg_uf = df_clean.groupby('uf')['valor'].mean().reset_index(name='receita_media')\n", "    # Mapeia sigla para código IBGE\n", "    agg_uf['uf_code'] = agg_uf['uf'].map(UF_TO_CODE_IBGE)\n", "    try:\n", "        geojson_states = load_states_geojson()\n", "        feature_key = infer_featureidkey(geojson_states)\n", "        # Tentativas de colunas possíveis para join\n", "        join_col_candidates = ['code_state','CD_GEOCUF','STATEFP','shapeID','sigla','abbrev_state']\n", "        matched_col = None\n", "        for c in join_col_candidates:\n", "            # coleta valores do geojson\n", "            vals = {f['properties'].get(c) for f in geojson_states['features'] if c in f['properties']}\n", "            # testa se códigos ou siglas batem\n", "            if agg_uf['uf_code'].dropna().astype(str).isin([str(v) for v in vals]).any():\n", "                matched_col = c\n", "                key_field = 'uf_code'\n", "                break\n", "            if agg_uf['uf'].dropna().isin([str(v) for v in vals]).any():\n", "                matched_col = c\n", "                key_field = 'uf'\n", "                break\n", "        if matched_col is None:\n", "            raise ValueError('<PERSON><PERSON><PERSON><PERSON> coluna de join encontrada no geojson.')\n", "\n", "        fig_choro = px.choropleth(\n", "            agg_uf,\n", "            geojson=geojson_states,\n", "            locations=key_field,\n", "            featureidkey=f'properties.{matched_col}',\n", "            color='receita_media',\n", "            color_continuous_scale='YlOrRd',\n", "            hover_name='uf',\n", "            title='<PERSON><PERSON><PERSON><PERSON> Receita M<PERSON>dia por UF'\n", "        )\n", "        fig_choro.update_geos(fitbounds='locations', visible=False)\n", "        fig_choro.write_html(REPORTS_PLOTS / 'choropleth_uf.html')\n", "        try:\n", "            fig_choro.write_image(REPORTS_PLOTS / 'choropleth_uf.png')\n", "        except Exception:\n", "            pass\n", "        fig_choro\n", "    except Exception as e:\n", "        import matplotlib.pyplot as plt\n", "        print('<PERSON>alha no choropleth, gerando ranking simples. Motivo:', e)\n", "        agg_uf.sort_values('receita_media', ascending=False, inplace=True)\n", "        plt.figure(figsize=(6,4))\n", "        plt.bar(agg_uf['uf'], agg_uf['receita_media'])\n", "        plt.title('Ranking <PERSON><PERSON><PERSON> por UF')\n", "        plt.tight_layout()\n", "        plt.savefig(REPORTS_PLOTS / 'ranking_uf.png')\n", "        agg_uf.head()\n", "else:\n", "    print('<PERSON><PERSON> uf ausente – pulando choropleth.')"]}, {"cell_type": "markdown", "id": "6495070e", "metadata": {}, "source": ["### 5.1 Índice de Confiabilidade por Loja (ICL)"]}, {"cell_type": "markdown", "id": "4729ca9c", "metadata": {}, "source": ["### 5.2 Detecção de Outliers Operacionais"]}, {"cell_type": "markdown", "id": "7771e0d9", "metadata": {}, "source": ["### 5.3 Padrões Intra-Semana (Micro-Sazonalidade)"]}, {"cell_type": "markdown", "id": "ff255319", "metadata": {}, "source": ["### 6.1 Forecast Curt<PERSON><PERSON><PERSON> (Operacional)"]}, {"cell_type": "code", "execution_count": null, "id": "ff5657c1", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Gerar baseline operacional média móvel curta + intervalo bootstrap.\n", "# Entradas: df_clean.\n", "# Saídas: forecast_curto.csv, forecast_examples.png.\n", "# Premissas: >=5 dias por loja.\n", "# Possíveis erros: <PERSON><PERSON> muito pequena para bootstrap.\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "curto_results = []\n", "plot_lojas = []\n", "for loja, g in df_clean.groupby('id_loja'):\n", "    g = g.sort_values('data')\n", "    s = g.set_index('data')['valor']\n", "    if len(s) < 3:\n", "        continue\n", "    window = min(5, max(3, len(s)//2))\n", "    forecast_point = s.tail(window).mean()\n", "    # bootstrap\n", "    samples = []\n", "    arr = s.tail(window).values\n", "    for _ in range(300):\n", "        samples.append(np.mean(np.random.choice(arr, size=len(arr), replace=True)))\n", "    low, high = np.percentile(samples, [10, 90])\n", "    curto_results.append({'id_loja': loja, 'forecast': forecast_point, 'p10': low, 'p90': high, 'window': window})\n", "    if len(plot_lojas) < 6:\n", "        plot_lojas.append((loja, s, forecast_point, low, high))\n", "\n", "curto_df = pd.DataFrame(curto_results)\n", "curto_df.to_csv(REPORTS_TABLES / 'forecast_curto.csv', index=False)\n", "\n", "# Plot exemplos\n", "plt.figure(figsize=(10,6))\n", "for i,(loja,s,fc,low,high) in enumerate(plot_lojas, start=1):\n", "    plt.subplot(3,2,i)\n", "    plt.plot(s.index, s.values, label='Histórico')\n", "    plt.axhline(fc, color='orange', label='Forecast' if i==1 else '')\n", "    plt.fill_between([s.index.min(), s.index.max()], low, high, color='orange', alpha=0.1)\n", "    plt.title(f'Loja {loja}')\n", "plt.tight_layout()\n", "plt.savefig(REPORTS_PLOTS / 'forecast_examples.png')\n", "\n", "curto_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f5691ea1", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: <PERSON><PERSON><PERSON> mé<PERSON> por dia da semana e contraste fim de semana.\n", "# Entradas: df_clean.\n", "# Saídas: micro_sazonalidade.csv, bar_dow.png.\n", "# Premissas: >=1 semana de dados (ajustamos se menos).\n", "# Possíveis erros: Poucos dados por DOW.\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "if 'data' in df_clean.columns:\n", "    df_clean['dow'] = df_clean['data'].dt.day_name(locale='pt_BR') if hasattr(df_clean['data'].dt,'day_name') else df_clean['data'].dt.dayofweek\n", "\n", "micro = df_clean.groupby('dow')['valor'].agg(media='mean', desvio='std', n='count')\n", "micro['coef_var'] = micro['desvio'] / micro['media'].replace(0,np.nan)\n", "micro.to_csv(REPORTS_TABLES / 'micro_sazonalidade.csv')\n", "\n", "plt.figure(figsize=(8,4))\n", "micro.sort_values('media')['media'].plot(kind='bar')\n", "plt.title('<PERSON><PERSON><PERSON> de Receita por Dia da Semana')\n", "plt.tight_layout()\n", "plt.savefig(REPORTS_PLOTS / 'bar_dow.png')\n", "\n", "micro.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ebd59cf2", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Identificar outliers por robust z-score e IsolationForest.\n", "# Entradas: df_clean.\n", "# Saídas: outliers.csv, scatter_outliers.png.\n", "# Premissas: >=5 observações por loja.\n", "# Possíveis erros: Falta de dados.\n", "\n", "from sklearn.ensemble import IsolationForest\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Robust z-score por loja\n", "def robust_z(group):\n", "    med = group['valor'].median()\n", "    mad = (group['valor'] - med).abs().median()\n", "    if mad == 0:\n", "        group['robust_z'] = 0.0\n", "    else:\n", "        group['robust_z'] = 0.6745 * (group['valor'] - med) / mad\n", "    return group\n", "\n", "rz_df = df_clean.groupby('id_loja', group_keys=False).apply(robust_z)\n", "rz_df['flag_robust'] = rz_df['robust_z'].abs() >= 3\n", "\n", "# Isolation Forest no agregado (data pivot de lojas?) usar valor direto\n", "iso = IsolationForest(random_state=42, contamination=0.05)\n", "rz_df['iso_flag'] = iso.fit_predict(rz_df[['valor']]) == -1\n", "\n", "outliers = rz_df[(rz_df['flag_robust']) | (rz_df['iso_flag'])].copy()\n", "outliers['motivo'] = np.where(outliers['flag_robust'] & outliers['iso_flag'], 'ambos',\n", "                              np.where(outliers['flag_robust'],'robust_z','isolation'))\n", "outliers.to_csv(REPORTS_TABLES / 'outliers.csv', index=False)\n", "\n", "plt.figure(figsize=(8,4))\n", "plt.scatter(rz_df['data'], rz_df['valor'], s=10, alpha=0.4, label='normal')\n", "plt.scatter(outliers['data'], outliers['valor'], color='red', s=15, label='outlier')\n", "plt.legend()\n", "plt.title('Outliers Operacionais')\n", "plt.tight_layout()\n", "plt.savefig(REPORTS_PLOTS / 'scatter_outliers.png')\n", "\n", "outliers.head()"]}, {"cell_type": "code", "execution_count": null, "id": "772dc05d", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Calcular ICL (m<PERSON><PERSON>, desvio, coef_var, iqr, taxa_dias_zero) e gerar heatmap + ranking.\n", "# Entradas: df_clean.\n", "# Saídas: icl.csv, heatmap_icl.png.\n", "# Premissas: Dados com >=1 dia por loja.\n", "# Possíveis erros: Divisão por zero se média=0.\n", "\n", "from src.analysis_stats import indice_confiabilidade_loja\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "icl = indice_confiabilidade_loja(df_clean)\n", "icl.to_csv(REPORTS_TABLES / 'icl.csv', index=False)\n", "\n", "plt.figure(figsize=(8,6))\n", "subset_cols = ['media','coef_var','iqr','taxa_dias_zero']\n", "heat_data = icl.set_index('id_loja')[subset_cols]\n", "sns.heatmap(heat_data.rank(pct=True), cmap='viridis')\n", "plt.title('Heatmap ICL (Ranks Percentuais)')\n", "plt.tight_layout()\n", "plt.savefig(REPORTS_PLOTS / 'heatmap_icl.png')\n", "\n", "icl.sort_values('coef_var', ascending=False).head()"]}, {"cell_type": "code", "execution_count": null, "id": "27e6bf95", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Gerar série diária total e top-6 lojas.\n", "# Entradas: df_clean.\n", "# Saídas: Arquivos de gráfico salvos.\n", "# Premissas: df_clean não vazio.\n", "# Possíveis erros: Erros de escrita de arquivo.\n", "\n", "import plotly.graph_objects as go\n", "\n", "serie_total = df_clean.groupby('data')['valor'].sum().sort_index()\n", "fig_total = go.Figure()\n", "fig_total.add_trace(go.<PERSON>er(x=serie_total.index, y=serie_total.values, mode='lines', name='Total'))\n", "fig_total.update_layout(title='Série Diária Total (Valor em Centavos)')\n", "fig_total.write_html(REPORTS_PLOTS / 'serie_diaria_total.html')\n", "fig_total.write_image(REPORTS_PLOTS / 'serie_diaria_total.png')\n", "\n", "# Top-6 lojas por receita total\n", "lojas_top = df_clean.groupby('id_loja')['valor'].sum().nlargest(6).index\n", "serie_top6 = df_clean[df_clean['id_loja'].isin(lojas_top)].groupby(['data','id_loja'])['valor'].sum().unstack('id_loja').sort_index()\n", "fig_top6 = go.Figure()\n", "for col in serie_top6.columns:\n", "    fig_top6.add_trace(go.<PERSON>(x=serie_top6.index, y=serie_top6[col], mode='lines', name=str(col)))\n", "fig_top6.update_layout(title='Série Diária Top-6 Lojas')\n", "fig_top6.write_html(REPORTS_PLOTS / 'serie_top6_lojas.html')\n", "fig_top6.write_image(REPORTS_PLOTS / 'serie_top6_lojas.png')\n", "\n", "serie_top6.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a8bdd3f2", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Boxplot por Tipo_PDV, heatmap UF x Tipo_PDV, Pareto lojas/produtos, Top/Bottom lojas.\n", "# Entradas: df_clean.\n", "# Saídas: PNG/HTML e tabelas em reports.\n", "# Premissas: Colunas opcionais podem não existir.\n", "# Possíveis erros: <PERSON><PERSON><PERSON>, poucos dados.\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import plotly.express as px\n", "from pathlib import Path\n", "\n", "if 'Tipo_PDV' in df_clean.columns:\n", "    receita_dia_tipo = df_clean.groupby(['data','Tipo_PDV'])['valor'].sum().reset_index()\n", "    fig_box = px.box(receita_dia_tipo, x='Tipo_PDV', y='valor', title='Boxplot Receita Diária por Tipo_PDV')\n", "    fig_box.write_html(REPORTS_PLOTS / 'boxplot_receita_tipo_pdv.html')\n", "    fig_box.write_image(REPORTS_PLOTS / 'boxplot_receita_tipo_pdv.png')\n", "\n", "if {'uf','Tipo_PDV'}.issubset(df_clean.columns):\n", "    heat = df_clean.groupby(['uf','Tipo_PDV'])['valor'].mean().reset_index()\n", "    pivot = heat.pivot(index='uf', columns='Tipo_PDV', values='valor')\n", "    import plotly.figure_factory as ff\n", "    z = pivot.fillna(0).values\n", "    x = list(pivot.columns)\n", "    y = list(pivot.index)\n", "    fig_heat = ff.create_annotated_heatmap(z, x=x, y=y, colorscale='Viridis', showscale=True)\n", "    fig_heat.update_layout(title='Heatmap Média de Receita por UF x Tipo_PDV')\n", "    fig_heat.write_html(REPORTS_PLOTS / 'heatmap_uf_tipo_pdv.html')\n", "    fig_heat.write_image(REPORTS_PLOTS / 'heatmap_uf_tipo_pdv.png')\n", "\n", "# Pareto lojas\n", "pareto_lojas = df_clean.groupby('id_loja')['valor'].sum().sort_values(ascending=False)\n", "pareto_lojas_cum = (pareto_lojas.cumsum() / pareto_lojas.sum()).reset_index()\n", "pareto_lojas_cum.columns = ['id_loja','cum_pct']\n", "pareto_lojas_cum.to_csv(REPORTS_TABLES / 'pareto_lojas.csv', index=False)\n", "\n", "# Pareto produtos (se existir)\n", "if 'id_produto' in df_clean.columns:\n", "    pareto_prod = df_clean.groupby('id_produto')['valor'].sum().sort_values(ascending=False)\n", "    pareto_prod_cum = (pareto_prod.cumsum() / pareto_prod.sum()).reset_index()\n", "    pareto_prod_cum.columns = ['id_produto','cum_pct']\n", "    pareto_prod_cum.to_csv(REPORTS_TABLES / 'pareto_produtos.csv', index=False)\n", "\n", "# Top/Bottom lojas por média diária e variância\n", "agg_loja = (df_clean.groupby('id_loja')\n", "            .agg(media_diaria=('valor','mean'), variancia=('valor','var'))\n", "            .reset_index())\n", "agg_loja.to_csv(REPORTS_TABLES / 'top_bottom_lojas.csv', index=False)\n", "\n", "# Gráfico Pareto lojas\n", "import matplotlib.pyplot as plt\n", "plt.figure(figsize=(8,4))\n", "pareto_lojas_cum_plot = pareto_lojas_cum.copy()\n", "plt.plot(range(1, len(pareto_lojas_cum_plot)+1), pareto_lojas_cum_plot['cum_pct'])\n", "plt.axhline(0.8, color='red', linestyle='--')\n", "plt.title('<PERSON><PERSON><PERSON> (Acumulado Receita)')\n", "plt.xlabel('Rank Loja')\n", "plt.ylabel('% Acumulado')\n", "plt.tight_layout()\n", "plt.savefig(REPORTS_PLOTS / 'pareto_lojas.png')\n", "\n", "if 'id_produto' in df_clean.columns:\n", "    pareto_prod_cum_plot = pareto_prod_cum\n", "    plt.figure(figsize=(8,4))\n", "    plt.plot(range(1,len(pareto_prod_cum_plot)+1), pareto_prod_cum_plot['cum_pct'])\n", "    plt.axhline(0.8, color='red', linestyle='--')\n", "    plt.title('Curva Pareto Produ<PERSON> (Acumulado Receita)')\n", "    plt.xlabel('Rank Produto')\n", "    plt.ylabel('% Acumulado')\n", "    plt.tight_layout()\n", "    plt.savefig(REPORTS_PLOTS / 'pareto_produtos.png')\n", "\n", "agg_loja.head()"]}, {"cell_type": "markdown", "id": "fc5bcd8e", "metadata": {}, "source": ["## 6. <PERSON><PERSON>\n", "\n", "**Objetivo:** Calcular previsões simples (média móvel 28d, sazonal ingênuo 7d) e avaliar em split temporal 80/20.\n", "**Entradas:** `df_clean` (valor di<PERSON>rio por loja).\n", "**<PERSON><PERSON><PERSON>:** `baseline_metrics.csv`, gráfico distribuição de erros.\n", "**Premissas:** Série por loja suficientemente longa.\n", "**Possíveis erros:** Poucos dados para janela / sazonalidade."]}, {"cell_type": "code", "execution_count": null, "id": "8758a575", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Calcular baselines e métricas.\n", "# Entradas: df_clean.\n", "# Saídas: baseline_metrics.csv e gráfico.\n", "# Premissas: <PERSON><PERSON> su<PERSON>.\n", "# Possíveis erros: Divisão por zero em métricas.\n", "\n", "from src.baselines import rolling_mean_baseline, seasonal_naive_baseline\n", "from src.metrics import wape, smape, mae\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "sales = df_clean.groupby(['id_loja','data'])['valor'].sum().reset_index()\n", "results = []\n", "valid_lojas = 0\n", "for loja, g in sales.groupby('id_loja'):\n", "    g = g.sort_values('data')\n", "    if len(g) < 30:  # pular séries muito curtas\n", "        continue\n", "    series = g.set_index('data')['valor']\n", "    split_idx = int(len(series)*0.8)\n", "    test = series.iloc[split_idx:]\n", "    if len(test) == 0:\n", "        continue\n", "    pred_rm = rolling_mean_baseline(series).iloc[split_idx:]\n", "    pred_sn = seasonal_naive_baseline(series).iloc[split_idx:]\n", "    # Ajuste para alinhamentos (fill) quando shift gera NaN\n", "    pred_rm = pred_rm.fillna(method='bfill').fillna(0)\n", "    pred_sn = pred_sn.fillna(method='bfill').fillna(0)\n", "\n", "    metrics = [\n", "        (loja, 'rolling_mean_28', wape(test, pred_rm), smape(test, pred_rm), mae(test, pred_rm)),\n", "        (loja, 'seasonal_naive_7', wape(test, pred_sn), smape(test, pred_sn), mae(test, pred_sn)),\n", "    ]\n", "    valid_lojas += 1\n", "    results.extend(metrics)\n", "\n", "baseline_metrics = pd.DataFrame(results, columns=['id_loja','baseline','WAPE','sMAPE','MAE'])\n", "baseline_metrics.to_csv(REPORTS_TABLES / 'baseline_metrics.csv', index=False)\n", "\n", "pct_valid = valid_lojas / sales['id_loja'].nunique() if sales['id_loja'].nunique() else 0\n", "print(f'Lojas válidas para baseline: {valid_lojas} ({pct_valid:.1%})')\n", "\n", "plt.figure(figsize=(8,4))\n", "baseline_metrics.boxplot(column='WAPE', by='baseline')\n", "plt.suptitle('Distribuição WAPE por Baseline')\n", "plt.title('')\n", "plt.tight_layout()\n", "plt.savefig(REPORTS_PLOTS / 'baseline_error_distribution.png')\n", "\n", "baseline_metrics.head()"]}, {"cell_type": "markdown", "id": "66bca3b9", "metadata": {}, "source": ["## 7. <PERSON><PERSON> (LightGBM)\n", "\n", "**Objetivo:** Treinar modelo de potencial diário (cross-sectional + tempo) usando features de calend<PERSON>rio, idade e categoria geográfica.\n", "**Entradas:** `df_clean` com features enriquecidas.\n", "**Sa<PERSON><PERSON>:** `gbm_metrics.csv`, gráficos Real vs Pred (Top-N) e feature_importance.\n", "**Premissas:** Pacote lightgbm instalado.\n", "**Possíveis erros:** Falha de import; overfitting se poucos dados; ausência de colunas categóricas."]}, {"cell_type": "code", "execution_count": null, "id": "db6dee12", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Treinar LightGBM e avaliar.\n", "# Entradas: df_clean.\n", "# Saídas: gbm_metrics.csv, feature_importance.csv, gráficos.\n", "# Premissas: lightgbm disponível.\n", "# Possíveis erros: <PERSON><PERSON><PERSON> de memória, colunas categóricas ausentes.\n", "\n", "from src.features import add_calendar_features, add_store_age\n", "import lightgbm as lgb\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "feat_df = add_store_age(add_calendar_features(df_clean.copy()))\n", "\n", "# Codificação simples de categóricas se existirem\n", "cat_cols = []\n", "for c in ['uf','cidade','Tipo_PDV']:\n", "    if c in feat_df.columns:\n", "        feat_df[c] = feat_df[c].astype('category')\n", "        cat_cols.append(c)\n", "\n", "target_col = 'valor'\n", "feature_cols = [c for c in feat_df.columns if c not in {target_col,'data'} and feat_df[c].dtype != 'O']\n", "\n", "X = feat_df[feature_cols].fillna(0)\n", "y = feat_df[target_col].astype(float)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=True, random_state=42)\n", "\n", "model = lgb.LGBMRegressor(n_estimators=300, learning_rate=0.05, max_depth=-1, random_state=42)\n", "model.fit(X_train, y_train)\n", "\n", "pred = model.predict(X_test)\n", "mae_model = mean_absolute_error(y_test, pred)\n", "rmse_model = mean_squared_error(y_test, pred, squared=False)\n", "\n", "metrics_df = pd.DataFrame([\n", "    {'metric':'MAE','value': mae_model},\n", "    {'metric':'RMSE','value': rmse_model}\n", "])\n", "metrics_df.to_csv(REPORTS_TABLES / 'gbm_metrics.csv', index=False)\n", "\n", "# Feature importance\n", "imp = pd.DataFrame({'feature': feature_cols, 'importance': model.feature_importances_}).sort_values('importance', ascending=False)\n", "imp.to_csv(REPORTS_TABLES / 'feature_importance.csv', index=False)\n", "\n", "plt.figure(figsize=(8,6))\n", "imp.head(20).plot(kind='barh', x='feature', y='importance')\n", "plt.title('Feature Importance (Top 20)')\n", "plt.tight_layout()\n", "plt.savefig(REPORTS_PLOTS / 'feature_importance.png')\n", "\n", "# Real vs Pred para top-N pontos (amostra aleatória de até 6 índices)\n", "plot_indices = np.random.choice(len(y_test), size=min(6, len(y_test)), replace=False)\n", "plt.figure(figsize=(10,6))\n", "for i, idx in enumerate(plot_indices, start=1):\n", "    plt.subplot(3,2,i)\n", "    plt.plot([0,1],[y_test.iloc[idx], pred[idx]], marker='o')\n", "    plt.xticks([0,1], ['Real','Pred'])\n", "    plt.title(f'Idx {y_test.index[idx]}')\n", "plt.tight_layout()\n", "plt.savefig(REPORTS_PLOTS / 'real_vs_pred_samples.png')\n", "\n", "metrics_df"]}, {"cell_type": "markdown", "id": "4bccc529", "metadata": {}, "source": ["## 8. Explainability (SHAP)\n", "\n", "**Objetivo:** Gerar explicabilidade global (summary) e top features.\n", "**Entradas:** Modelo treinado e `X_test`.\n", "**<PERSON><PERSON><PERSON>:** `shap_summary.png`, `shap_top_features.csv`.\n", "**Premissas:** <PERSON><PERSON> shap instalado.\n", "**Possíveis erros:** Erros de compatibilidade de versão shap/lightgbm."]}, {"cell_type": "code", "execution_count": null, "id": "ca429bd7", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Calcular valores SHAP e salvar gráficos/tabelas.\n", "# Entradas: model, X_test.\n", "# Saídas: shap_summary_sample.png, shap_top_features.csv.\n", "# Premissas: SHAP instalado.\n", "# Possíveis erros: Versão incompatível.\n", "\n", "import shap, numpy as np, pandas as pd\n", "shap.initjs()\n", "# <PERSON><PERSON> até 500 linhas para performance\n", "sample_size = min(500, len(X_test))\n", "X_sample = X_test.sample(sample_size, random_state=42) if len(X_test) > sample_size else X_test\n", "explainer = shap.<PERSON>Explainer(model)\n", "shap_values = explainer.shap_values(X_sample)\n", "\n", "plt.figure(figsize=(8,6))\n", "shap.summary_plot(shap_values, X_sample, show=False, max_display=20)\n", "plt.tight_layout()\n", "plt.savefig(REPORTS_PLOTS / 'shap_summary_sample.png')\n", "\n", "mean_abs = np.abs(shap_values).mean(axis=0)\n", "shap_rank = pd.DataFrame({'feature': X_sample.columns, 'mean_abs_shap': mean_abs}).sort_values('mean_abs_shap', ascending=False)\n", "shap_rank.head(10).to_csv(REPORTS_TABLES / 'shap_top_features.csv', index=False)\n", "shap_rank.head(10)"]}, {"cell_type": "markdown", "id": "08fe0b9c", "metadata": {}, "source": ["## 9. Export & Sumário\n", "\n", "**Objetivo:** Consolidar conclusões e checar checklist DoD.\n", "**Entradas:** Artefatos gerados previamente.\n", "**Saídas:** Lista de conclusões e verificação final.\n", "**Premissas:** Todas as etapas completas.\n", "**Possíveis erros:** Arquivos ausentes (etapas não executadas)."]}, {"cell_type": "code", "execution_count": null, "id": "7f060363", "metadata": {}, "outputs": [], "source": ["# Objetivo | Entradas | Saídas | Premissas | Possíveis erros\n", "# Objetivo: Exibir checklist de conclusão e conclusões + gerar summary.csv.\n", "# Entradas: Artefatos de etapas anteriores.\n", "# Saídas: Prints de status + summary.csv + conclusões iniciais.\n", "# Premissas: Execução completa.\n", "# Possíveis erros: Arquivo não encontrado.\n", "\n", "from pathlib import Path\n", "import pandas as pd\n", "import json, glob\n", "\n", "checks = {\n", "    'cleaned.csv': (DATA_CLEAN / 'cleaned.csv').exists(),\n", "    'baseline_metrics.csv': (REPORTS_TABLES / 'baseline_metrics.csv').exists(),\n", "    'gbm_metrics.csv': (REPORTS_TABLES / 'gbm_metrics.csv').exists(),\n", "    '>=6 plots': len(list(REPORTS_PLOTS.glob('*.png'))) >= 6,\n", "    'choropleth_ou_ranking': any([(REPORTS_PLOTS / 'choropleth_uf.png').exists(), (REPORTS_PLOTS / 'ranking_uf.png').exists()]),\n", "    'qa_overview.csv': (REPORTS_TABLES / 'qa_overview.csv').exists(),\n", "    'summary.csv': <PERSON><PERSON><PERSON>,\n", "    'data_dictionary.md': (BASE_DIR / 'docs' / 'data_dictionary.md').exists(),\n", "}\n", "print('Checklist:')\n", "for k,v in checks.items():\n", "    print(f' - {k}:', 'OK' if v else 'PENDENTE')\n", "\n", "# Monta summary\n", "periodo = None\n", "lojas = None\n", "receita_total = None\n", "try:\n", "    df_loaded = pd.read_csv(DATA_CLEAN / 'cleaned.csv', parse_dates=['data'])\n", "    periodo = f\"{df_loaded['data'].min().date()}→{df_loaded['data'].max().date()}\"\n", "    lojas = df_loaded['id_loja'].nunique()\n", "    receita_total = int(df_loaded['valor'].sum())\n", "except Exception:\n", "    pass\n", "\n", "mae_rmse = {}\n", "try:\n", "    gbm = pd.read_csv(REPORTS_TABLES / 'gbm_metrics.csv')\n", "    for _,r in gbm.iterrows():\n", "        mae_rmse[r['metric']] = r['value']\n", "except Exception:\n", "    pass\n", "\n", "try:\n", "    base_metrics = pd.read_csv(REPORTS_TABLES / 'baseline_metrics.csv')\n", "    lojas_baseline = base_metrics['id_loja'].nunique()\n", "except Exception:\n", "    lojas_baseline = 0\n", "\n", "summary_row = {\n", "    'periodo': periodo,\n", "    'lojas': lojas,\n", "    'receita_total_centavos': receita_total,\n", "    'n_plots_png': len(list(REPORTS_PLOTS.glob('*.png'))),\n", "    'lojas_com_baseline': lojas_baseline,\n", "    'MAE_modelo': mae_rmse.get('MAE'),\n", "    'RMSE_modelo': mae_rmse.get('RMSE'),\n", "}\n", "summary_df = pd.DataFrame([summary_row])\n", "summary_df.to_csv(REPORTS_TABLES / 'summary.csv', index=False)\n", "checks['summary.csv'] = True\n", "\n", "print('\\nSummary:')\n", "print(summary_df)\n", "\n", "conclusoes = [\n", "    'Placeholder: <PERSON><PERSON>ita concentrada em X% das lojas (atualizar após análise).',\n", "    'Placeholder: Variação sazonal semanal observada (atualizar).',\n", "    'Placeholder: Modelo LightGBM supera baselines (ver MAE/RMSE).',\n", "    'Placeholder: Feature importance dominada por idade_da_loja / calend<PERSON>rio.',\n", "    'Placeholder: Cobertura de Tipo_PDV limitada (atualizar %).'\n", "]\n", "print('\\nConclusões preliminares:')\n", "for c in conclusoes:\n", "    print(' -', c)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}