{"cells": [{"cell_type": "code", "execution_count": null, "id": "8f3281d2", "metadata": {}, "outputs": [], "source": ["# ===========================================\n", "# Teste de Normalidade (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)\n", "# Variáveis: <PERSON>or_Total, <PERSON><PERSON><PERSON>, Total_Preco_Liquido\n", "# ===========================================\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.stats import kstest\n", "\n", "# === Parâmetros (ajuste o caminho do arquivo) ===\n", "PATH = \"var_artefas - Página1.csv\"\n", "ALPHA = 0.05\n", "\n", "# === 1) Carregar base e converter para numérico (vírgula decimal -> ponto) ===\n", "try:\n", "    df = pd.read_csv(PATH)\n", "except UnicodeDecodeError:\n", "    df = pd.read_csv(PATH, encoding=\"latin1\")\n", "\n", "for col in df.columns:\n", "    df[col] = (\n", "        df[col].astype(str)\n", "               .str.replace(\".\", \"\", regex=False)    # remove separador de milhar\n", "               .str.replace(\",\", \".\", regex=False)    # vírgula -> ponto\n", "    )\n", "    df[col] = pd.to_numeric(df[col], errors=\"coerce\")\n", "\n", "# === 2) Selecionar variáveis e aplicar KS (comparando com N(0,1) após z-score) ===\n", "variaveis = {\n", "    \"Valor_Total\": df[\"Valor_Total\"].dropna(),\n", "    \"Desconto\": df[\"Desconto\"].dropna(),\n", "    \"Total_Preco_Liquido\": df[\"Total_Preco_Liquido\"].dropna()\n", "}\n", "\n", "linhas = []\n", "for nome, serie in variaveis.items():\n", "    # padroniza: média 0, <PERSON><PERSON> 1\n", "    z = (serie - serie.mean()) / serie.std(ddof=0)\n", "    ks_stat, p_value = kstest(z, \"norm\")\n", "    conclusao = \"Rejeita H0 (não normal)\" if p_value <= ALPHA else \"Não rejeita H0 (pode ser normal)\"\n", "    linhas.append([nome, ks_stat, p_value, serie.mean(), serie.median(), conclusao])\n", "\n", "# === 3) Tabela de resultados ===\n", "res = pd.DataFrame(linhas, columns=[\n", "    \"Variável\", \"KS Estatística\", \"p-valor\", \"Média\", \"Mediana\", \"Conclusão\"\n", "])\n", "\n", "# Impress<PERSON> amigável\n", "res_fmt = res.copy()\n", "res_fmt[\"KS Estatística\"] = res_fmt[\"KS Estatística\"].map(lambda x: f\"{x:.3f}\")\n", "res_fmt[\"p-valor\"] = res_fmt[\"p-valor\"].map(lambda x: f\"{x:.3g}\")     # exibe 0.000 como 0.000 / 2.3e-10 etc\n", "res_fmt[\"Média\"] = res_fmt[\"Média\"].map(lambda x: f\"{x:,.2f}\".replace(\",\", \"X\").replace(\".\", \",\").replace(\"X\", \".\"))\n", "res_fmt[\"Mediana\"] = res_fmt[\"Mediana\"].map(lambda x: f\"{x:,.2f}\".replace(\",\", \"X\").replace(\".\", \",\").replace(\"X\", \".\"))\n", "\n", "print(\"\\nResultados do teste KS (<PERSON><PERSON><PERSON>rov–Smirnov):\\n\")\n", "print(res_fmt.to_string(index=False))\n", "\n", "# === 4) (Opcional) Exportar ===\n", "res.to_csv(\"resultado_teste_KS.csv\", index=False)\n", "\n", "md = (\n", "    \"| Variável | KS (estatística) | p-valor | Média | Mediana | Conclusão |\\n\"\n", "    \"|---|---:|---:|---:|---:|---|\\n\" +\n", "    \"\\n\".join(\n", "        f\"| {row['Variável']} | {row['KS Estatística']:.3f} | {row['p-valor']:.3g} | \"\n", "        f\"{row['<PERSON><PERSON><PERSON>']:.2f} | {row['Mediana']:.2f} | {row['Conclusão']} |\"\n", "        for _, row in res.iterrows()\n", "    )\n", ")\n", "with open(\"resultado_teste_KS.md\", \"w\", encoding=\"utf-8\") as f:\n", "    f.write(md)\n", "\n", "print(\"\\nArquivos salvos:\")\n", "print(\"- resultado_teste_KS.csv\")\n", "print(\"- resultado_teste_KS.md\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "48bbc48a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}