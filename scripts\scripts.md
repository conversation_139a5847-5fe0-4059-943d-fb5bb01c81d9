# Scripts do Projeto InfoPepper

Esta pasta contém scripts de automação e análise do projeto.

## Scripts Disponíveis

### `final_analysis.py`
Script principal de análise final do projeto.

**Uso:**
```bash
python scripts/final_analysis.py
```

### `validate_structure.py`
Script para validação da estrutura de dados e arquivos do projeto.

**Uso:**
```bash
python scripts/validate_structure.py
```

### `generate_corr.py`
Script para geração de matrizes de correlação e análises estatísticas.

**Uso:**
```bash
python scripts/generate_corr.py
```

### `generate_pipeline_diagram.py`
Script para geração de diagramas do pipeline de dados e metodologia.

**Uso:**
```bash
python scripts/generate_pipeline_diagram.py
```

## Execução

Para executar qualquer script, certifique-se de estar no diretório raiz do projeto e ter o ambiente virtual ativado:

```bash
# Ativar ambiente virtual
.venv\Scripts\activate  # Windows
# ou
source .venv/bin/activate  # Linux/Mac

# Executar script
python scripts/nome_do_script.py
```

## Dependências

Os scripts dependem das bibliotecas listadas em `requirements.txt`. Instale-as com:

```bash
pip install -r requirements.txt
```
