#Implementa modelos baseline simples para previsão de séries temporais diárias
from __future__ import annotations
import pandas as pd


def rolling_mean_baseline(s: pd.Series, window: int = 28) -> pd.Series:
    # Implementa baseline usando média móvel dos últimos N dias (default=28)
    # Usa min_periods=1 para calcular média mesmo com poucos dados iniciais
    # Desloca 1 dia para frente (shift) para evitar usar dados do futuro
    return s.rolling(window, min_periods=1).mean().shift(1)


def seasonal_naive_baseline(s: pd.Series, season: int = 7) -> pd.Series:
    # Implementa baseline sazonal que repete valores do último ciclo
    # Default season=7 para padrão semanal (usa valor da semana anterior)
    # Exemplo: previsão de quinta usa valor da quinta passada
    return s.shift(season)

# Lista de funções disponíveis para importação
__all__ = ['rolling_mean_baseline', 'seasonal_naive_baseline']
