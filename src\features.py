"""Geração de features para modelo cross-sectional de potencial."""
from __future__ import annotations
import pandas as pd

 #Adiciona features relacionadas a data e hora ao DataFrame.
def add_calendar_features(df: pd.DataFrame) -> pd.DataFrame:
   
    # Verifica se existe a coluna 'data' antes de prosseguir
    if 'data' not in df.columns:
        return df
    
    # Extrai o dia da semana (0=Segunda, 6=Domingo)
    df['dow'] = df['data'].dt.dayofweek
    
    # Extrai o mês (1-12)
    df['mes'] = df['data'].dt.month
    
    # Extrai o ano
    df['ano'] = df['data'].dt.year
    
    # Calcula a semana do ano (1-53)
    df['semana_do_ano'] = df['data'].dt.isocalendar().week.astype(int)
    
    # Cria indicador de fim de semana (1 se Sábado/Domingo, 0 caso contrário)
    df['is_fim_semana'] = (df['dow'] >= 5).astype(int)
    
    return df

    #Calcula a idade de cada loja com base na data de abertura.
def add_store_age(df: pd.DataFrame) -> pd.DataFrame:

    # Verifica se existem as colunas necessárias
    if {'data','id_loja'}.issubset(df.columns):
        # Encontra a primeira data de operação de cada loja
        first_dates = df.groupby('id_loja')['data'].transform('min')
        
        # Calcula quantos dias se passaram desde a primeira operação
        df['idade_da_loja'] = (df['data'] - first_dates).dt.days
    return df

__all__ = ['add_calendar_features', 'add_store_age']
